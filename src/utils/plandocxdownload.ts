import Docxtemplater from 'docxtemplater'
import <PERSON>z<PERSON><PERSON> from 'pizzip'
import PizZipUtils from 'pizzip/utils/index.js'
import { saveAs } from 'file-saver'
function loadFile(url, callback) {
  PizZipUtils.getBinaryContent(url, callback)
}
/**
 * 将Object 的空值转换为空字符串
 */
function emptyValue(obj) {
  const returnValue = {}
  for (const key in obj) {
    if (obj[key] === null || obj[key] === undefined) {
      returnValue[key] = ' '
    }  else {
      returnValue[key] = obj[key]
    }
  }
  return returnValue
}

/**
 * 
 * @param row 
 * @param fileName 
 */
export const downloadPlanTemplate = (row: object,fileName:string) => {
  const url = window.location.origin + '/template/planTemplate.docx'
  console.log(url)

  loadFile(url, function (error, content) {
    if (error) {
      throw error
    }
    const zip = new PizZip(content)
    const doc = new Docxtemplater(zip, { paragraphLoop: true, linebreaks: true })

    doc.render(emptyValue({
      planCode: '',
      planType: 1,
      planName: '张三',
      planStatus: 2,
      projectCode: '',
      projectName: '张三',
      supplierId: 15504,
      supplierName: '张三',
      contentType: 2,
      contentCode: '',
      fileUrl: 'https://www.iocoder.cn',
      fileUrlList: [],
      createTime: '',
      remark: '你说的对',
      ...row
    }))

    const out = doc.getZip().generate({
      type: 'blob',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    // Output the document using Data-URI
    saveAs(out, fileName+'.docx')
  })
}


export const downloadArrivalInspectionTemplate = (row: object,fileName:string) => {
  const url = window.location.origin + '/template/ArrivalInspectionTemplate.docx'
  console.log(url)

  loadFile(url, function (error, content) {
    if (error) {
      throw error
    }
    const zip = new PizZip(content)
    const doc = new Docxtemplater(zip, { paragraphLoop: true, linebreaks: true })
    const targetValue = emptyValue({
      planCode: '',
      planType: 1,
      planName: '张三',
      planStatus: 2,
      projectCode: '',
      projectName: '张三',
      supplierId: 15504,
      supplierName: '张三',
      contentType: 2,
      contentCode: '',
      fileUrl: 'https://www.iocoder.cn',
      fileUrlList: [],
      createTime: '',
      remark: '你说的对',
      ...row
    })
    console.log(targetValue);
    

    doc.render(targetValue)

    const out = doc.getZip().generate({
      type: 'blob',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    // Output the document using Data-URI
    saveAs(out, fileName+'.docx')
  })
}
