import { initGlobalState, MicroAppStateActions } from 'qiankun'

// 定义全局状态类型
interface GlobalState {
  token?: string
  userInfo?: Record<string, any>
  // 其他需要共享的状态
}

// 初始化全局状态
const initialState: GlobalState = {
  token: '',
  userInfo: {}
}

// 初始化通信actions
const actions: MicroAppStateActions = initGlobalState(initialState)

// 监听状态变化
actions.onGlobalStateChange((state, prev) => {
  console.log('主应用状态变更:', state, prev)
})

export default actions
