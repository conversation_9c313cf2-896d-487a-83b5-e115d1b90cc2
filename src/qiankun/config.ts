import { getAccessToken } from '@/utils/auth'

// 微应用配置
export const microApps = [
  {
    name: 'micro-app', // 微应用名称
    entry: '//localhost:7101', // 微应用入口
    container: '#subapp', // 微应用挂载节点
    activeRule: '/micro-app', // 微应用激活规则
    sandbox: {
      // strictStyleIsolation: true, // 开启严格的样式隔离
      // experimentalStyleIsolation: true, // 开启实验性的样式隔离
      // // 添加以下配置增强样式隔离
      // cssVariables: true, // 允许CSS变量穿透
      // styleSheetTransform: true, // 启用样式表转换
    },
    props: {
      // 传递给微应用的props
      basePath: '/micro-app',
      // 传递主应用token
      token: getAccessToken(),
      // token更新回调
      onTokenUpdate: (newToken: string | null) => {
        if (newToken) {
          localStorage.setItem('ACCESS_TOKEN', newToken)
        } else {
          localStorage.removeItem('ACCESS_TOKEN')
        }
      }
    }
  }
]
