// qiankun 微前端开发环境启动脚本
import { start } from 'qiankun';
import { microApps } from './config';

// 开发环境配置
const devConfig = {
  sandbox: {
    experimentalStyleIsolation: true // 开启样式隔离
  },
  prefetch: false // 开发环境禁用预加载
};

// 注册微应用
function registerMicroApps() {
  return microApps.map(app => ({
    ...app,
    props: {
      ...app.props,
      isDev: true // 开发环境标识
    }
  }));
}

// 启动 qiankun
start({
  ...devConfig,
  apps: registerMicroApps()
});

console.log('qiankun 微前端开发环境已启动');
