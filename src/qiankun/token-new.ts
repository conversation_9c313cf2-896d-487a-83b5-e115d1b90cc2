import { getTenantId, getAccessToken as getToken, setToken, removeToken } from '@/utils/auth'

/**
 * iframe微前端通信通道
 * 提供主应用与iframe微前端之间的安全通信机制
 * 支持令牌同步、自定义消息类型和事件监听
 */
class IframeCommunicator {
  // 自定义消息处理器
  private customHandlers: Record<string, (payload: any, source: Window) => void> = {}

  // 构造函数
  constructor() {
    // 绑定方法确保this指向
    this.handleMessage = this.handleMessage.bind(this)
  }

  // 初始化通信
  init() {
    this.destroy()
    window.addEventListener('message', this.handleMessage)
    console.log('[IframeCommunicator 父] 主系统通信通道已初始化')
  }

  // 销毁通信
  destroy() {
    window.removeEventListener('message', this.handleMessage)
    console.log('[IframeCommunicator 父] 通信通道已销毁')
  }

  // 处理接收到的消息
  handleMessage(event: MessageEvent) {
    // 验证消息来源
    if (!this.isValidOrigin(event.origin)) {
      console.warn(`[IframeCommunicator 父] 来自未授权源的消息: ${event.origin}`)
      return
    }

    console.log('object :>> 接收子消息222222222222', event.origin, event.data.type)

    const { type, payload } = event.data

    // 处理内置消息类型
    switch (type) {
      case 'TOKEN_UPDATE':
        setToken(payload.token)
        console.log('[IframeCommunicator 父] 令牌已更新')
        break
      case 'TOKEN_REQUEST':
        this.sendToken()
        console.log('object :>> ', '发送token')
        break
      case 'TOKEN_REMOVE':
        removeToken()
        console.log('[IframeCommunicator 父] 令牌已移除')
        break
      default:
        // 处理自定义消息
        if (this.customHandlers[type]) {
          this.customHandlers[type](payload, event.source)
        } else {
          console.log(`[IframeCommunicator 父] 未处理的消息类型: ${type}`)
        }
    }
  }

  // 注册自定义消息处理器
  on(messageType: string, handler: (payload: any, source: Window) => void) {
    this.customHandlers[messageType] = handler
  }

  // 移除自定义消息处理器
  off(messageType: string) {
    delete this.customHandlers[messageType]
  }

  // 向iframe发送token
  sendToken(targetOrigin: string = '*') {
    console.log('[IframeCommunicator 父] 发送令牌到iframe微前端')
    const token = getToken()
    const tenantId = getTenantId()
    this.postMessage(
      {
        type: 'TOKEN_UPDATE',
        payload: {
          token,
          tenantId
        }
      },
      targetOrigin
    )
  }

  // 向父应用请求token
  requestToken(targetOrigin: string = '*') {
    console.log('object :>>向父应用请求token ')
    this.postMessage(
      {
        type: 'TOKEN_REQUEST'
      },
      targetOrigin
    )
  }

  // 通知移除token
  removeToken(targetOrigin: string = '*') {
    this.postMessage(
      {
        type: 'TOKEN_REMOVE'
      },
      targetOrigin
    )
  }

  // 向父/子应用发送消息
  postMessage(message: any, targetOrigin: string = '*') {
    try {
      if (window.parent !== window) {
        // 微前端上下文 - 发送给父应用
        window.parent.postMessage(message, targetOrigin)
      } else {
        // 主应用上下文 - 发送给所有iframe
        document.querySelectorAll('iframe').forEach((iframe) => {
          try {
            if (iframe.src) {
              const iframeOrigin = new URL(iframe.src).origin
              iframe.contentWindow?.postMessage(message, iframeOrigin)
            }
          } catch (error) {
            console.error('[IframeCommunicator 父] 发送消息到iframe失败:', error)
          }
        })
      }
    } catch (error) {
      console.error('[IframeCommunicator 父] 消息发送失败:', error)
    }
  }

  // 验证消息来源（添加允许的域名）
  private isValidOrigin(origin: string): boolean {
    const allowedOrigins = [window.location.origin]
    return allowedOrigins.includes(origin)
  }
}

// 创建单例实例
const communicator = new IframeCommunicator()

// 自动初始化
try {
  if (window.parent !== window) {
    communicator.init()
  }
} catch (error) {
  console.error('[IframeCommunicator 父] 初始化失败:', error)
}

// 导出单例实例
export default communicator