<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="产品编号" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-select
              v-model="row.productId"
              clearable
              filterable
              placeholder="请选择产品"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品数量" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productCount`" :rules="formRules.productCount" class="mb-0px!">
            <el-input-number
              v-model="row.productCount"
              controls-position="right"
              :min="0.001"
              :precision="3"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品单位" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productUnitId`" :rules="formRules.productUnitId" class="mb-0px!">
            <el-select
              v-model="row.productUnitId"
              clearable
              placeholder="请选择单位"
              class="!w-100%"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="包装箱号" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.packageNo`" class="mb-0px!">
            <el-input v-model="row.packageNo" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="进场检查" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.needApproachCheck`" class="mb-0px!">
            <el-select
            v-model="row.needApproachCheck"
            placeholder="请选择"
            class="!w-100%"
          >
            <el-option
              v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_NEED_APPROACH_CHECK)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="外观" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.appearance`" :rules="formRules.appearance" class="mb-0px!">
            <el-input v-model="row.appearance" placeholder="请输入外观检查结果" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="外包装" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.outPacking`" :rules="formRules.outPacking" class="mb-0px!">
            <el-input v-model="row.outPacking" placeholder="请输入外包装检查结果" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备品备件" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.spareParts`" :rules="formRules.spareParts" class="mb-0px!">
            <el-input v-model="row.spareParts" placeholder="请输入备品备件检查结果" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="设备合格证" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.deviceQc`" :rules="formRules.deviceQc" class="mb-0px!">
            <el-input v-model="row.deviceQc" placeholder="请输入设备合格证检查结果" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品合格证" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productQc`" :rules="formRules.productQc" class="mb-0px!">
            <el-input v-model="row.productQc" placeholder="请输入产品合格证检查结果" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品说明书" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productManual`" :rules="formRules.productManual" class="mb-0px!">
            <el-input v-model="row.productManual" placeholder="请输入产品说明书检查结果" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开箱日期" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.openBoxTime`" :rules="formRules.openBoxTime" class="mb-0px!">
            <el-date-picker
              v-model="row.openBoxTime"
              type="datetime"
              placeholder="选择开箱日期"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加验收产品</el-button>
  </el-row>
</template>

<script setup lang="ts">
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { ProductUnitApi, ProductUnitVO } from '@/api/erp/product/unit'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import type { FormInstance } from 'element-plus'

interface ApproachInspectionItem {
  id?: number
  inspectionId?: string
  productId?: number
  productUnitId?: number
  productCount?: number
  packageNo?: string
  remark?: string
  needApproachCheck?: number // 1:需要 0:不需要
}

const props = defineProps<{
  items: ApproachInspectionItem[]
  disabled: boolean
}>()

const formLoading = ref(false)
const formData = ref<ApproachInspectionItem[]>([])
const formRules = reactive({
  productId: [{ required: true, message: '产品编号不能为空', trigger: 'blur' }],
  productUnitId: [{ required: true, message: '产品单位不能为空', trigger: 'blur' }],
  productCount: [{ required: true, message: '产品数量不能为空', trigger: 'blur' }],
  needApproachCheck: [{ required: true, message: '请选择进场检查', trigger: 'change' }]
})
const formRef = ref<FormInstance>()
const productList = ref<ProductVO[]>([])
const unitList = ref<ProductUnitVO[]>([])

/** 初始化数据 */
watch(
  () => props.items,
  (val) => {
    formData.value = val || []
  },
  { immediate: true }
)

/** 添加产品项 */
const handleAdd = () => {
  formData.value.push({
    id: undefined,
    inspectionId: undefined,
    productId: undefined,
    productUnitId: undefined,
    productCount: 1,
    packageNo: undefined,
    remark: undefined,
    needApproachCheck: 1 // 默认需要进场检查
  })
}

/** 删除产品项 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单验证 */
const validate = () => {
  return formRef.value?.validate()
}
defineExpose({ validate })

/** 初始化数据 */
onMounted(async () => {
  productList.value = await ProductApi.getProductSimpleList()
  unitList.value = await ProductUnitApi.getProductUnitSimpleList()
  // 默认添加一个空行
  if (formData.value.length === 0 && !props.disabled) {
    handleAdd()
  }
})
</script>
