

## 创建进场开箱验收单


**接口地址**:`/materials/approach-inspection/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 32117,
  "inspectionCode": "",
  "inspectionType": 1,
  "inspectionName": "赵六",
  "inspectionAddr": "",
  "inspectionTime": "",
  "inspectionStatus": 1,
  "projectCode": "",
  "projectName": "赵六",
  "supplierId": 9098,
  "contractNo": "",
  "contractName": "李四",
  "builderDept": "",
  "productNames": "",
  "fileUrl": "https://www.iocoder.cn",
  "remark": "你说的对",
  "processInstanceId": "",
  "processStatus": 0,
  "items": [
    {
      "id": 20498,
      "inspectionId": "",
      "productId": 4929,
      "productUnitId": 7366,
      "productCount": 10788,
      "packageNo": "",
      "remark": "你说的对",
      "needApproachCheck": 1
    }
  ]
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|approachInspectionSaveReqVO|管理后台 - 进场开箱验收单新增/修改 Request VO|body|true|ApproachInspectionSaveReqVO|ApproachInspectionSaveReqVO|
|&emsp;&emsp;id|主键ID||true|integer(int64)||
|&emsp;&emsp;inspectionCode|检查单编号||true|string||
|&emsp;&emsp;inspectionType|检查单类型||true|string||
|&emsp;&emsp;inspectionName|检查单名称||true|string||
|&emsp;&emsp;inspectionAddr|检查地址,进场地址||true|string||
|&emsp;&emsp;inspectionTime|检查时间,进场时间||true|string(date-time)||
|&emsp;&emsp;inspectionStatus|检查状态||false|string||
|&emsp;&emsp;projectCode|工程编号||true|string||
|&emsp;&emsp;projectName|工程名称||true|string||
|&emsp;&emsp;supplierId|供应商编号||true|integer(int64)||
|&emsp;&emsp;contractNo|合同编号||true|string||
|&emsp;&emsp;contractName|合同名称||true|string||
|&emsp;&emsp;builderDept|施工单位||true|string||
|&emsp;&emsp;productNames|产品名称||false|string||
|&emsp;&emsp;fileUrl|附件地址||false|string||
|&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;processInstanceId|||false|string||
|&emsp;&emsp;processStatus|||false|integer(int32)||
|&emsp;&emsp;items|物资 进场开箱验收单单项列表||false|array|Item|
|&emsp;&emsp;&emsp;&emsp;id|编号||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;inspectionId|检查单Id||true|string||
|&emsp;&emsp;&emsp;&emsp;productId|产品编号||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;productUnitId|产品单位单位||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;productCount|产品数量||true|number||
|&emsp;&emsp;&emsp;&emsp;packageNo|包装箱号||false|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;&emsp;&emsp;needApproachCheck|是否需要进场报审||true|integer(int32)||



## 删除进场开箱验收单


**接口地址**:`/materials/approach-inspection/delete`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|编号|query|true|integer(int64)||



## 导出进场开箱验收单 Excel


**接口地址**:`/materials/approach-inspection/export-excel`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码，从 1 开始|query|true|integer(int32)||
|pageSize|每页条数，最大值为 100|query|true|integer(int32)||
|inspectionCode|检查单编号|query|false|string||
|inspectionType|检查单类型|query|false|string||
|inspectionName|检查单名称|query|false|string||
|inspectionAddr|检查地址,进场地址|query|false|string||
|inspectionTime|检查时间,进场时间|query|false|array|string|
|inspectionStatus|检查状态|query|false|string||
|projectCode|工程编号|query|false|string||
|projectName|工程名称|query|false|string||
|supplierId|供应商编号|query|false|integer(int64)||
|contractNo|合同编号|query|false|string||
|contractName|合同名称|query|false|string||
|builderDept|施工单位|query|false|string||
|productNames|产品名称|query|false|string||
|fileUrl|附件地址|query|false|string||
|createTime|创建时间|query|false|array|string|
|remark|备注|query|false|string||



## 获得进场开箱验收单


**接口地址**:`/materials/approach-inspection/get`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|编号|query|true|integer(int64)||


**响应状态**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||ApproachInspectionRespVO|ApproachInspectionRespVO|
|&emsp;&emsp;id|主键ID|integer(int64)||
|&emsp;&emsp;inspectionCode|检查单编号|string||
|&emsp;&emsp;inspectionType|检查单类型|string||
|&emsp;&emsp;inspectionName|检查单名称|string||
|&emsp;&emsp;inspectionAddr|检查地址,进场地址|string||
|&emsp;&emsp;inspectionTime|检查时间,进场时间|string(date-time)||
|&emsp;&emsp;inspectionStatus|检查状态|string||
|&emsp;&emsp;projectCode|工程编号|string||
|&emsp;&emsp;projectName|工程名称|string||
|&emsp;&emsp;supplierId|供应商编号|integer(int64)||
|&emsp;&emsp;contractNo|合同编号|string||
|&emsp;&emsp;contractName|合同名称|string||
|&emsp;&emsp;builderDept|施工单位|string||
|&emsp;&emsp;productNames|产品名称|string||
|&emsp;&emsp;fileUrl|附件地址|string||
|&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;processInstanceId||string||
|&emsp;&emsp;processStatus||integer(int32)||
|&emsp;&emsp;items|物资 进场开箱验收单单项列表|array|Item|
|&emsp;&emsp;&emsp;&emsp;id|编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;inspectionId|检查单Id|string||
|&emsp;&emsp;&emsp;&emsp;productId|产品编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;productUnitId|产品单位单位|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;productCount|产品数量|number||
|&emsp;&emsp;&emsp;&emsp;packageNo|包装箱号|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;needApproachCheck|是否需要进场报审|integer(int32)||
|msg||string||



## 获得进场开箱验收单分页


**接口地址**:`/materials/approach-inspection/page`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码，从 1 开始|query|true|integer(int32)||
|pageSize|每页条数，最大值为 100|query|true|integer(int32)||
|inspectionCode|检查单编号|query|false|string||
|inspectionType|检查单类型|query|false|string||
|inspectionName|检查单名称|query|false|string||
|inspectionAddr|检查地址,进场地址|query|false|string||
|inspectionTime|检查时间,进场时间|query|false|array|string|
|inspectionStatus|检查状态|query|false|string||
|projectCode|工程编号|query|false|string||
|projectName|工程名称|query|false|string||
|supplierId|供应商编号|query|false|integer(int64)||
|contractNo|合同编号|query|false|string||
|contractName|合同名称|query|false|string||
|builderDept|施工单位|query|false|string||
|productNames|产品名称|query|false|string||
|fileUrl|附件地址|query|false|string||
|createTime|创建时间|query|false|array|string|
|remark|备注|query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPageResultApproachInspectionRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PageResultApproachInspectionRespVO|PageResultApproachInspectionRespVO|
|&emsp;&emsp;list|数据|array|ApproachInspectionRespVO|
|&emsp;&emsp;&emsp;&emsp;id|主键ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;inspectionCode|检查单编号|string||
|&emsp;&emsp;&emsp;&emsp;inspectionType|检查单类型|string||
|&emsp;&emsp;&emsp;&emsp;inspectionName|检查单名称|string||
|&emsp;&emsp;&emsp;&emsp;inspectionAddr|检查地址,进场地址|string||
|&emsp;&emsp;&emsp;&emsp;inspectionTime|检查时间,进场时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;inspectionStatus|检查状态|string||
|&emsp;&emsp;&emsp;&emsp;projectCode|工程编号|string||
|&emsp;&emsp;&emsp;&emsp;projectName|工程名称|string||
|&emsp;&emsp;&emsp;&emsp;supplierId|供应商编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;contractNo|合同编号|string||
|&emsp;&emsp;&emsp;&emsp;contractName|合同名称|string||
|&emsp;&emsp;&emsp;&emsp;builderDept|施工单位|string||
|&emsp;&emsp;&emsp;&emsp;productNames|产品名称|string||
|&emsp;&emsp;&emsp;&emsp;fileUrl|附件地址|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;processInstanceId||string||
|&emsp;&emsp;&emsp;&emsp;processStatus||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;items|物资 进场开箱验收单单项列表|array|Item|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;inspectionId|检查单Id|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;productId|产品编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;productUnitId|产品单位单位|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;productCount|产品数量|number||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;packageNo|包装箱号|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;needApproachCheck|是否需要进场报审|integer(int32)||
|&emsp;&emsp;total|总量|integer(int64)||
|msg||string||



## 更新进场开箱验收单


**接口地址**:`/materials/approach-inspection/update`


**请求方式**:`PUT`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 32117,
  "inspectionCode": "",
  "inspectionType": 1,
  "inspectionName": "赵六",
  "inspectionAddr": "",
  "inspectionTime": "",
  "inspectionStatus": 1,
  "projectCode": "",
  "projectName": "赵六",
  "supplierId": 9098,
  "contractNo": "",
  "contractName": "李四",
  "builderDept": "",
  "productNames": "",
  "fileUrl": "https://www.iocoder.cn",
  "remark": "你说的对",
  "processInstanceId": "",
  "processStatus": 0,
  "items": [
    {
      "id": 20498,
      "inspectionId": "",
      "productId": 4929,
      "productUnitId": 7366,
      "productCount": 10788,
      "packageNo": "",
      "remark": "你说的对",
      "needApproachCheck": 1
    }
  ]
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|approachInspectionSaveReqVO|管理后台 - 进场开箱验收单新增/修改 Request VO|body|true|ApproachInspectionSaveReqVO|ApproachInspectionSaveReqVO|
|&emsp;&emsp;id|主键ID||true|integer(int64)||
|&emsp;&emsp;inspectionCode|检查单编号||true|string||
|&emsp;&emsp;inspectionType|检查单类型||true|string||
|&emsp;&emsp;inspectionName|检查单名称||true|string||
|&emsp;&emsp;inspectionAddr|检查地址,进场地址||true|string||
|&emsp;&emsp;inspectionTime|检查时间,进场时间||true|string(date-time)||
|&emsp;&emsp;inspectionStatus|检查状态||false|string||
|&emsp;&emsp;projectCode|工程编号||true|string||
|&emsp;&emsp;projectName|工程名称||true|string||
|&emsp;&emsp;supplierId|供应商编号||true|integer(int64)||
|&emsp;&emsp;contractNo|合同编号||true|string||
|&emsp;&emsp;contractName|合同名称||true|string||
|&emsp;&emsp;builderDept|施工单位||true|string||
|&emsp;&emsp;productNames|产品名称||false|string||
|&emsp;&emsp;fileUrl|附件地址||false|string||
|&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;processInstanceId|||false|string||
|&emsp;&emsp;processStatus|||false|integer(int32)||
|&emsp;&emsp;items|物资 进场开箱验收单单项列表||false|array|Item|
|&emsp;&emsp;&emsp;&emsp;id|编号||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;inspectionId|检查单Id||true|string||
|&emsp;&emsp;&emsp;&emsp;productId|产品编号||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;productUnitId|产品单位单位||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;productCount|产品数量||true|number||
|&emsp;&emsp;&emsp;&emsp;packageNo|包装箱号||false|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;&emsp;&emsp;needApproachCheck|是否需要进场报审||true|integer(int32)||
