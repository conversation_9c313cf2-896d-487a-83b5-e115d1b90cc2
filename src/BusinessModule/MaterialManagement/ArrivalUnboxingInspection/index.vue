<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="85px"
    >
      <el-form-item label="检查单编号" prop="inspectionCode">
        <el-input
          v-model="queryParams.inspectionCode"
          class="!w-240px"
          clearable
          placeholder="请输入检查单编号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查单类型" prop="inspectionType">
        <el-select
          v-model="queryParams.inspectionType"
          class="!w-240px"
          clearable
          placeholder="请选择检查单类型"
        >
          <el-option
            v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_INSPECTION_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检查时间" prop="inspectionTime">
        <el-date-picker
          v-model="queryParams.inspectionTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          v-hasPermi="['material:approach-inspection:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" />
          新增验收
        </el-button>
        <el-button
          v-hasPermi="['material:approach-inspection:export']"
          :loading="exportLoading"
          plain
          type="success"
          @click="handleExport"
        >
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table :show-overflow-tooltip="true" v-loading="loading" :data="list">
      <el-table-column min-width="150" align="center" label="检查单编号" prop="inspectionCode" />
      <el-table-column min-width="150" align="center" label="检查单类型" prop="inspectionType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_INSPECTION_TYPE" :value="scope.row.inspectionType" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="检查单名称" prop="inspectionName" />
      <el-table-column min-width="150" align="center" label="检查地址" prop="inspectionAddr" />
      <el-table-column min-width="150" align="center" label="工程编号" prop="projectCode" />
      <el-table-column min-width="150" align="center" label="工程名称" prop="projectName" />
      <el-table-column min-width="150" align="center" label="供应商编号" prop="supplierId" />
      <el-table-column min-width="150" align="center" label="合同编号" prop="contractNo" />
      <el-table-column min-width="150" align="center" label="合同名称" prop="contractName" />
      <el-table-column min-width="150" align="center" label="施工单位" prop="builderDept" />
      <el-table-column min-width="150" align="center" label="产品名称" prop="productNames" />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="检查时间"
        prop="inspectionTime"
        width="180"
      />
      <el-table-column align="center" label="备注" prop="remark" />
      <el-table-column align="center" label="检查状态" prop="inspectionStatus">
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.MATERIAL_INSPECTION_STATUS"
            :value="scope.row.inspectionStatus"
          />
        </template>
      </el-table-column>
      <el-table-column :width="240" align="center" label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['material:approach-inspection:docx']"
            link
            type="primary"
            @click="handleDocx(scope.row)"
          >
            出验收单
          </el-button>
          <el-button
            v-hasPermi="['material:approach-inspection:update']"
            link
            type="primary"
            @click="openForm('edit', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['material:approach-inspection:detail']"
            link
            type="primary"
            @click="openDetail(scope.row.id)"
          >
            详情
          </el-button>
          <el-button
            v-hasPermi="['material:approach-inspection:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as ApproachInspectionApi from '@/api/material/ArrivalUnboxingInspection'
import router from '@/router'

defineOptions({ name: 'ApproachInspection' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 表单弹窗的显示状态

const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  inspectionCode: '',
  inspectionType: undefined,
  inspectionName: '',
  inspectionAddr: '',
  inspectionTime: [],
  inspectionStatus: undefined,
  projectCode: '',
  projectName: '',
  supplierId: undefined,
  contractNo: '',
  contractName: '',
  builderDept: '',
  productNames: ''
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询验收单列表 */
const getList = async () => {
  loading.value = true
  try {
    // 对queryParams 清除空值
    Object.keys(queryParams).forEach((key) => {
      if (queryParams[key] === '' || queryParams[key] === undefined) {
        delete queryParams[key]
      }
    })
    const data = await ApproachInspectionApi.getApproachInspectionPage(queryParams)
    list.value = data.list
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const dialogType = ref('edit')

/**
 * 打开详情页
 * @param route 跳转的路由名称
 * @param type 操作类型
 * @param id ID
 */
const openDetailPage = (route: string, type: string, id: number) => {
  router.push({ name: route })
  localStorage.setItem('ApproachInspectionMode', type)
  localStorage.setItem('ApproachInspectionId', `${id}`)
}

const openForm = (type: string, id?: number) => {
  dialogType.value = type
  openDetailPage('ApproachInspectionEdit', type, id)

}

/** 详情操作 */
const openDetail = (id: number) => {
  dialogType.value = 'readonly'
  openDetailPage('ApproachInspectionDetail', 'readonly', id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ApproachInspectionApi.deleteApproachInspection(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ApproachInspectionApi.exportApproachInspection(queryParams)
    download.excel(data, '进场开箱验收单列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 生成验收单文档 */
const handleDocx = (row: any) => {
  // TODO: 实现生成验收单文档逻辑
  console.log('生成验收单文档', row)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
