<template>
  <ContentWrap>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="mode === 'readonly'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工程名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入工程名称" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程编号" prop="projectCode">
            <el-input v-model="form.projectCode" placeholder="请输入工程编号" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检查单名称" prop="inspectionName">
            <el-input
              v-model="form.inspectionName"
              placeholder="请输入检查单名称"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查单编号" prop="inspectionCode">
            <el-input
              v-model="form.inspectionCode"
              placeholder="请输入检查单编号"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检查单类型" prop="inspectionType">
            <el-select
              v-model="form.inspectionType"
              placeholder="请选择检查单类型"
              class="!w-240px"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_INSPECTION_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查状态" prop="inspectionStatus">
            <el-select
              v-model="form.inspectionStatus"
              placeholder="请选择检查状态"
              class="!w-240px"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_INSPECTION_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供应商" prop="supplierId">
            <el-select
              v-model="form.supplierId"
              placeholder="请选择供应商"
              class="!w-240px"
              filterable
              clearable
            >
              <el-option
                v-for="supplier in supplierList"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="施工单位" prop="builderDept">
            <el-input v-model="form.builderDept" placeholder="请输入施工单位" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="检查时间" prop="inspectionTime">
            <el-date-picker
              v-model="form.inspectionTime"
              type="datetime"
              placeholder="选择检查时间"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <ContentWrap>
        <el-tabs v-model="subTabsName" class="-mt-15px -mb-10px">
          <el-tab-pane label="订单产品清单" name="item">
            <PurchaseOrderItemForm
              ref="itemFormRef"
              :items="form.items"
              :disabled="mode === 'readonly'"
            />
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="检查地址" prop="inspectionAddr">
            <el-input
              v-model="form.inspectionAddr"
              type="textarea"
              :rows="3"
              placeholder="请输入检查地址"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="外观检查" prop="appearance">
            <el-input
              v-model="form.appearance"
              type="textarea"
              :rows="3"
              placeholder="请输入外观检查结果"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="意见" prop="opinion">
            <el-input
              v-model="form.opinion"
              type="textarea"
              :rows="3"
              placeholder="请输入意见"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="附件" prop="fileUrl">
            <UploadFile
              :is-show-tip="false"
              :model-value="form.fileUrlList"
              @update:model-value="form.fileUrlList = $event || []"
            />
          </el-form-item>
        </el-col>

        <el-col :span="20">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="flex justify-center mt-4">
      <el-button @click="close">
        {{ mode !== 'readonly'?'取 消': '关 闭' }}
      </el-button>
      <el-button v-if="mode !== 'readonly'" type="primary" @click="submitForm">
        确 定
      </el-button>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useTagsView } from '@/hooks/web/useTagsView'
import { ElMessage } from 'element-plus'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import * as InspectionApi from '@/api/material/inspection/index'
import type { ArrivalInspectionSaveReqVO } from '@/api/material/inspection'
import type { ProductVO } from '@/api/erp/product/product'
import router from '@/router'

import PurchaseOrderItemForm from './components/PurchaseOrderItemForm.vue'

// 根据接口文档更新表单类型定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: ''
  }
})

const mode = ref(localStorage.getItem('ArrivalInspectionManagementmode') || 'edit')
const projectID = ref<number | undefined>(
  Number(localStorage.getItem('ArrivalInspectionManagementid')) || undefined
)

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const supplierList = ref<Array<{ id: number; name: string }>>([])

const productList = ref<Array<ProductVO>>([])

const detailId = ref<number | undefined>(undefined)

// 表单初始值
const form = reactive({
  inspectionCode: '',
  inspectionType: '',
  inspectionName: '',
  inspectionAddr: '',
  inspectionTime: '',
  projectCode: localStorage.getItem('ArrivalInspectionManagementprojectCode') || '',
  projectName: localStorage.getItem('ArrivalInspectionManagementprojectName') || '',
  supplierId: 0,
  builderDept: localStorage.getItem('ArrivalInspectionManagementbuilderDept') || '',
  productName: '',
  productKey: '',
  productNum: 0,
  productSpecification: '',
  appearance: '',
  opinion: '',
  fileUrl: '',
  fileUrlList: [],
  remark: '',
  items: []
} as ArrivalInspectionSaveReqVO)

const rules = reactive({
  inspectionCode: [{ required: true, message: '检查单编号不能为空', trigger: 'blur' }],
  inspectionType: [{ required: true, message: '检查单类型不能为空', trigger: 'change' }],
  inspectionStatus: [{ required: false, message: '请选择检查状态', trigger: 'change' }],
  projectCode: [{ required: true, message: '工程编号不能为空', trigger: 'blur' }],
  projectName: [{ required: true, message: '工程名称不能为空', trigger: 'blur' }],
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  productKey: [{ required: false, message: '请输入产品标识', trigger: 'blur' }],
  productSpecification: [{ required: false, message: '请输入产品规格', trigger: 'blur' }]
})

// 打开弹窗
// 获取供应商列表
const getSupplierList = async () => {
  try {
    // TODO: 替换为实际的供应商API调用
    const res = await InspectionApi.getSupplierSimpleList()
    supplierList.value = res.map((item: { id: number; name: string }) => ({
      id: item.id,
      name: item.name
    }))
  } catch (err) {
    console.error('获取供应商列表失败:', err)
  }
}

const getProductList = async () => {
  try {
    const res = await InspectionApi.getProductSimpleList()
    productList.value = res.map((item: ProductVO) => ({
      id: item.id,
      name: item.name,
      standard: item.standard,
      barCode: item.barCode
    }))
  } catch (err) {
    console.error('获取产品列表失败:', err)
  }
}

const InitALL = async () => {
  await Promise.all([getSupplierList(), getProductList()])
  dialogVisible.value = true

  detailId.value = projectID.value
  // 编辑模式，获取详情
  if (detailId.value) {
    const res = await InspectionApi.getInspectionDetail(detailId.value)
    Object.assign(form, reactive(res))
  }
}

// 关闭弹窗
const { closeCurrent } = useTagsView()

const close = () => {
  // 保存表单值到localStorage
  localStorage.setItem('ArrivalInspectionManagementmode', '')
  localStorage.setItem('ArrivalInspectionManagementid', '')

  dialogVisible.value = false
  formRef.value?.resetFields()
  detailId.value = undefined
  form.items = []

  // 先关闭当前tag再回退路由
  const currentRoute = unref(router.currentRoute)
  closeCurrent(currentRoute)
  // 使用setTimeout确保tags关闭后再回退
  setTimeout(() => {
    router.go(-1)
  }, 50)
}

const subTabsName = ref('item') // 子表标签名

// 提交表单
const submitForm = async () => {
  // 保存表单值到localStorage

  console.log('提交表单', form)
  try {
    await formRef.value.validate()

    const submitData: InspectionApi.ArrivalInspectionSaveReqVO = {
      ...form,
      id: projectID.value || detailId.value,
      items: form.items
    }

    if (submitData.id) {
      // 编辑
      await InspectionApi.updateInspection(submitData)
    } else {
      // 新增
      await InspectionApi.createInspection(submitData)
    }

    ElMessage.success('操作成功')
    close()
    emit('success')
  } catch (err: any) {
    console.error('表单提交错误:', err)
    ElMessage.error(err.message || '操作失败，请重试')
  }
}

InitALL()
</script>
