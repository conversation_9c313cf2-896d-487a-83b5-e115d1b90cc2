<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="产品编号" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-select
              v-model="row.productId"
              clearable
              filterable
              @change="onChangeProduct($event, row)"
              placeholder="请选择产品"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品数量" prop="productCount" fixed="right" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productCount`" :rules="formRules.productCount" class="mb-0px!">
            <el-input-number
              v-model="row.productCount"
              controls-position="right"
              :min="0.001"
              :precision="3"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品单位单位" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productUnitId`" :rules="formRules.productUnitId" class="mb-0px!">
            <el-select
              v-model="row.productUnitId"
              clearable
              placeholder="请选择单位"
              class="!w-100%"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="包装箱号" prop="packageNo" fixed="right" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.packageNo`" class="mb-0px!">
            <el-input v-model="row.packageNo" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="进场检查" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.needApproachCheck`" class="mb-0px!">
            <el-select
              v-model="row.needApproachCheck"
              placeholder="请选择"
              class="!w-100%"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_NEED_APPROACH_CHECK)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加采购产品</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { ProductUnitApi, ProductUnitVO } from '@/api/erp/product/unit'
import { StockApi } from '@/api/erp/stock/stock'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import type { FormInstance } from 'element-plus'

interface PurchaseOrderItem {
  id?: number
  inspectionId?: string // 检查单Id
  productId?: number // 产品编号
  productUnitId?: number // 产品单位单位
  productUnitName?: string // 产品单位名称（逻辑中使用）
  productBarCode?: string // 产品条码（逻辑中使用）
  stockCount?: number // 库存数量（逻辑中使用）
  productCount?: number // 产品数量
  packageNo?: string // 包装箱号
  remark?: string // 备注
  needApproachCheck?: string // 是否需要进场检查
}

const props = defineProps<{
  items: PurchaseOrderItem[]
  disabled: boolean
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<PurchaseOrderItem[]>([])
const formRules = reactive({
  productId: [{ required: true, message: '产品编号不能为空', trigger: 'blur' }],
  productUnitId: [{ required: true, message: '产品单位单位不能为空', trigger: 'blur' }],
  productCount: [{ required: true, message: '产品数量不能为空', trigger: 'blur' }]
})
const formRef = ref<FormInstance>() // 表单 Ref
const productList = ref<ProductVO[]>([]) // 产品列表
const unitList = ref<ProductUnitVO[]>([]) // 单位列表

/** 初始化设置入库项 */
watch(
  () => props.items,
  async (val) => {
    formData.value = val || []
  },
  { immediate: true }
)
/** 新增按钮操作 */
const handleAdd = () => {
  const row: PurchaseOrderItem = {
    id: undefined,
    inspectionId: undefined,
    productId: undefined,
    productUnitId: undefined,
    productUnitName: undefined,
    productBarCode: undefined,
    stockCount: undefined,
    productCount: 1,
    packageNo: undefined,
    remark: undefined,
    needApproachCheck: undefined
  }
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 处理产品变更 */
const onChangeProduct = (productId, row) => {
  const product = productList.value.find((item) => item.id === productId)
  if (product) {
    row.productUnitName = product.unitName
    row.productBarCode = product.barCode
    row.productPrice = product.purchasePrice
  }
  // 加载库存
  setStockCount(row)
}

/** 加载库存 */
const setStockCount = async (row: any) => {
  if (!row.productId) {
    return
  }
  const count = await StockApi.getStockCount(row.productId)
  row.stockCount = count || 0
}

/** 表单校验 */
const validate = () => {
  return formRef.value?.validate()
}
defineExpose({ validate })

/** 初始化 */
onMounted(async () => {
  productList.value = await ProductApi.getProductSimpleList()
  unitList.value = await ProductUnitApi.getProductUnitSimpleList()
  // 默认添加一个
  if (formData.value.length === 0) {
    handleAdd()
  }
})
</script>
