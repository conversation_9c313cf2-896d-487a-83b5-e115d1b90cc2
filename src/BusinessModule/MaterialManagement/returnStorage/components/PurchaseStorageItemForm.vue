<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label=" " type="index" align="center" width="40" />
      <el-table-column label="物资名称" min-width="120" prop="name">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.name`" :rules="formRules.name" class="mb-0px!">
            <el-input v-model="row.name" placeholder="请输入物资名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="仓库" min-width="120" prop="warehouseId">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" class="mb-0px!">
            <el-select
              v-model="row.warehouseId"
              placeholder="请选择仓库"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in warehouseList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150" prop="remark">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60" v-if="!disabled">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加入库物料</el-button>
  </el-row>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import type { Item as PurchaseStorageItem } from '@/api/material/purchaseStorage'
import { WarehouseApi } from '@/api/erp/stock/warehouse'

const props = defineProps<{
  items: PurchaseStorageItem[]
  disabled: boolean
}>()

const formLoading = ref(false)
const formData = ref<PurchaseStorageItem[]>([])
const warehouseList = ref([])

// 获取仓库列表
const getWarehouseList = async () => {
    const res = await WarehouseApi.getWarehouseSimpleList()
    warehouseList.value  = res
}

const formRules = reactive({
  name: [{ required: true, message: '物资名称不能为空', trigger: 'blur' }],
  category: [{ required: true, message: '物资分类不能为空', trigger: 'blur' }],
  unit: [{ required: true, message: '计量单位不能为空', trigger: 'blur' }],
  arrivalQuantity: [{ required: true, message: '到货数量不能为空', trigger: 'blur' }]
})
const formRef = ref<FormInstance>()

/** 初始化数据 */
watch(
  () => props.items,
  (val) => {
    formData.value = val || []
  },
  { immediate: true }
)

/** 添加物料项 */
const handleAdd = () => {
  formData.value.push({
    id: undefined,
    name: '',
    warehouseId: undefined,
    remark: ''
  })
}

/** 删除物料项 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单验证 */
const validate = () => {
  return formRef.value?.validate()
}
defineExpose({ validate })

// 初始化时获取仓库列表
onMounted(() => {
  getWarehouseList()
  if (formData.value.length === 0 && !props.disabled) {
    handleAdd()
  }
})

</script>
