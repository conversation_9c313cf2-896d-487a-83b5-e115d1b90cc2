<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="85px"
    >
      <el-form-item label="入库单号" prop="no">
        <el-input
          v-model="queryParams.no"
          class="!w-240px"
          clearable
          placeholder="请输入入库单号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入库类型" prop="type">
        <el-select
          v-model="queryParams.type"
          class="!w-240px"
          clearable
          placeholder="请选择入库类型"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_STORAGE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入库状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="!w-240px"
          clearable
          placeholder="请选择入库状态"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_STORAGE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          v-hasPermi="['material:purchase-storage:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" />
          新增入库
        </el-button>
        <el-button
          v-hasPermi="['material:purchase-storage:export']"
          :loading="exportLoading"
          plain
          type="success"
          @click="handleExport"
        >
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column min-width="150" align="center" label="入库单号" prop="no" />
      <el-table-column min-width="150" align="center" label="入库类型" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_STORAGE_TYPE" :value="scope.row.storageType" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="入库状态" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_STORAGE_STATUS" :value="scope.row.storageStatus" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="供应商编号" prop="supplierId" />
      <el-table-column min-width="150" align="center" label="供应商名称" prop="supplierName" />
      <el-table-column min-width="150" align="center" label="物料总数" prop="totalCount" />
      <el-table-column min-width="150" align="center" label="总金额" prop="totalAmount" />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="创建时间"
        prop="createTime"
        width="180"
      />
      <el-table-column min-width="150" align="center" label="备注" prop="remark" />
      <el-table-column min-width="150" :width="260" align="center" label="操作" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['material:purchase-storage:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['material:purchase-storage:detail']"
            link
            type="primary"
            @click="openDetail(scope.row.id)"
          >
            详情
          </el-button>
          <el-button
            v-hasPermi="['material:purchase-storage:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改/详情 -->
  <PurchaseStorageForm
    ref="formRef"
    :model-value="dialogVisible"
    :mode="dialogType"
    @update:model-value="dialogVisible = !!$event"
    @success="getList"
  />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import {
  getAogOrderPage as getAogInPage,
  deleteAogOrder as deleteAogIn,
  exportAogOrder as exportAogIn
} from '@/api/material/purchaseStorage'
import PurchaseStorageForm from './PurchaseStorageForm.vue'

defineOptions({ name: 'MaterialPurchaseStorage' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 表单弹窗的显示状态
const dialogType = ref('edit') // 表单弹窗的类型

const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  no: '',
  type: undefined,
  status: undefined,
  supplierId: undefined,
  supplierName: '',
  totalCount: undefined,
  totalAmount: undefined,
  createTime: [],
  remark: ''
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询采购入库列表 */
const getList = async () => {
  loading.value = true
  try {
    // 对queryParams 清除空值
    Object.keys(queryParams).forEach((key) => {
      if (queryParams[key] === '' || queryParams[key] === undefined) {
        delete queryParams[key]
      }
    })
    const data = await getAogInPage(queryParams)
    list.value = data.list
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  dialogType.value = 'edit'
  formRef.value.open(type, id)
}

/** 详情操作 */
const openDetail = (id: number) => {
  dialogType.value = 'readonly'
  formRef.value.open('', id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteAogIn([id])
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await exportAogIn(queryParams)
    download.excel(data, '采购入库列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
