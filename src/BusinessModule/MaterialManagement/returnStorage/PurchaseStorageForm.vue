<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="close">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="mode === 'readonly'"
    >
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采购单号" prop="purchaseOrderNo">
            <el-input
              v-model="form.purchaseOrderNo"
              placeholder="请输入采购单号"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库类型" prop="inType">
            <el-select v-model="form.inType" placeholder="请选择入库类型" class="!w-240px">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_STORAGE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入库时间" prop="orderTime">
            <el-date-picker
              v-model="form.orderTime"
              type="datetime"
              placeholder="选择入库时间"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库操作员" prop="operatorIn">
            <el-input v-model="form.operatorIn" placeholder="请输入入库操作员" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="需求计划编号" prop="requirementNo">
            <el-input
              v-model="form.requirementNo"
              placeholder="请输入物资需求计划编号"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">到货信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采购订单号" prop="purchaseOrderNo">
            <el-input
              v-model="form.purchaseOrderNo"
              placeholder="请输入采购订单号"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商" prop="supplierId">
            <el-select
              v-model="form.supplierId"
              placeholder="请选择供应商"
              class="!w-240px"
              filterable
              clearable
            >
              <el-option
                v-for="supplier in supplierList"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="到货单据" prop="fileUrl">
            <UploadFile
              :is-show-tip="false"
              :model-value="form.fileUrl"
              @update:model-value="form.fileUrl = $event || ''"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">物资清单</el-divider>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item prop="items">
            <div class="-mt-10px w-full">
              <PurchaseStorageItemForm
                ref="itemFormRef"
                :items="form.items"
                :disabled="mode === 'readonly'"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">入库检验</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检验结论" prop="inspectionResult">
            <el-select
              v-model="form.inspectionResult"
              placeholder="请选择检验结论"
              class="!w-240px"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_INSPECTION_RESULT)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检验人员" prop="inspectionUserId">
            <el-input
              v-model="form.inspectionUserId"
              placeholder="请输入检验人员"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="检验意见" prop="inspectionOpinion">
            <el-input
              v-model="form.inspectionOpinion"
              type="textarea"
              :rows="2"
              placeholder="请输入检验意见"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="检验附件" prop="deliveryFileUrl">
            <UploadFile
              :is-show-tip="false"
              :model-value="form.deliveryFileUrl"
              @update:model-value="form.deliveryFileUrl = $event || ''"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">入库操作</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入库操作" prop="inType">
            <el-select v-model="form.inType" placeholder="请选择入库操作" class="!w-240px">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_STORAGE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库仓库" prop="warehouseId">
            <el-select
              v-model.number="form.warehouseId"
              placeholder="请选择入库仓库"
              class="!w-240px"
              clearable
              filterable
            >
              <el-option
                v-for="item in warehouseList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="仓库分区" prop="warehousePartitionId">
            <el-select
              v-model.number="form.warehousePartitionId"
              placeholder="请选择仓库分区"
              class="!w-500px"
              clearable
              filterable
              :disabled="!form.warehouseId"
            >
              <el-option
                v-for="item in partitionList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库操作类型" prop="inCategory">
            <el-select v-model="form.inCategory" placeholder="请选择入库操作类型" class="!w-240px">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_IN_CATEGORY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="退货操作员" prop="operatorReturn">
            <el-input
              v-model="form.operatorReturn"
              placeholder="请输入退货操作员"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left">签名确认</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="质检员签名" prop="signQc">
            <el-input v-model="form.signQc" placeholder="请输入质检员签名" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="仓管员签名" prop="signWarehouse">
            <el-input
              v-model="form.signWarehouse"
              placeholder="请输入仓管员签名"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目负责人签名" prop="signProjectLeader">
            <el-input
              v-model="form.signProjectLeader"
              placeholder="请输入项目负责人签名"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物资部签名" prop="signMaterial">
            <el-input v-model="form.signMaterial" placeholder="请输入物资部签名" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="close">取 消</el-button>
      <el-button v-if="mode !== 'readonly'" type="primary" @click="submitForm"> 确 定 </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { WarehouseApi } from '@/api/erp/stock/warehouse'
import {
  getAogOrderDetail as getPurchaseStorageDetail,
  createAogOrder as createPurchaseStorage,
  updateAogOrder as updatePurchaseStorage
} from '@/api/material/purchaseStorage'
import * as InspectionApi from '@/api/material/inspection/index'
import PurchaseStorageItemForm from './components/PurchaseStorageItemForm.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  id: {
    type: Number,
    default: undefined
  },
  mode: {
    type: String,
    default: 'edit', // 'edit' | 'readonly'
    validator: (value: string) => ['edit', 'readonly'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const itemFormRef = ref()
const supplierList = ref<Array<{ id: number; name: string }>>([])
const warehouseList = ref<Array<{ id: number; name: string }>>([])
const partitionList = ref<Array<{ id: number; name: string }>>([])

const detailId = ref<number | undefined>(undefined)

// 表单初始值
const form = reactive({
  id: undefined,
  supplierId: 0,
  orderTime: '',
  fileUrl: '',
  remark: '',
  inspectionAddr: '',
  appearance: '',
  deliveryNo: '',
  deliveryType: '',
  purchaseOrderNo: '',
  receiveType: '',
  receiveUserId: '',
  qcResult: '',
  qcUnqualifiedReason: '',
  qcOpinion: '',
  inspectionResult: '',
  inspectionOpinion: '',
  warehouseId: 0,
  warehousePartitionId: 0,
  signQc: '',
  signWarehouse: '',
  signProjectLeader: '',
  operatorIn: '',
  operatorReturn: '',
  signMaterial: '',
  inType: '',
  deliveryFileUrl: '',
  requirementId: 0,
  requirementNo: '',
  purchaseOrderId: 0,
  inspectionUserId: '',
  inspectionDeptId: '',
  inCategory: '',
  items: []
})

const rules = reactive({
  purchaseOrderNo: [{ required: true, message: '采购单号不能为空', trigger: 'blur' }],
  supplierId: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  items: [{ required: true, message: '请添加物资清单', trigger: 'blur' }]
})

// 获取供应商列表
const getSupplierList = async () => {
  try {
    const res = await InspectionApi.getSupplierSimpleList()
    supplierList.value = res.map((item: { id: number; name: string }) => ({
      id: item.id,
      name: item.name
    }))
  } catch (err) {
    console.error('获取供应商列表失败:', err)
  }
}

const open = async (type: string, id?: number) => {
  await getSupplierList()
  dialogVisible.value = true
  detailId.value = props.id || id
  if (detailId.value) {
    // 编辑模式，获取详情
    const res = await getPurchaseStorageDetail(detailId.value)
    Object.assign(form, res)
  }
}

// 关闭弹窗
const close = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
  detailId.value = undefined
  emit('update:modelValue', false)
}

// 提交表单
const submitForm = async () => {
  try {
    await Promise.all([formRef.value.validate(), itemFormRef.value?.validate()])

    const submitData = {
      ...form,
      id: props.id || detailId.value
    }

    if (submitData.id) {
      // 编辑
      await updatePurchaseStorage(submitData)
    } else {
      // 新增
      await createPurchaseStorage(submitData)
    }

    ElMessage.success('操作成功')
    close()
    emit('success')
  } catch (err: any) {
    console.error('表单提交错误:', err)
    ElMessage.error(err.message || '操作失败，请重试')
  }
}

// 获取仓库列表
const getWarehouseList = async () => {
  try {
    const res = await WarehouseApi.getWarehouseSimpleList()
    warehouseList.value = res
  } catch (err) {
    console.error('获取仓库列表失败:', err)
  }
}

// 获取仓库分区列表
const getPartitionList = async (warehouseId: number) => {
  try {
    if (!warehouseId) {
      partitionList.value = []
      return
    }
    const res = await WarehouseApi.listPartition(warehouseId)
    partitionList.value = res
  } catch (err) {
    console.error('获取仓库分区列表失败:', err)
  }
}

// 监听仓库ID变化
watch(
  () => form.warehouseId,
  (newVal) => {
    getPartitionList(newVal)
  }
)

onMounted(() => {
  getWarehouseList()
  if (form.warehouseId) {
    getPartitionList(form.warehouseId)
  }
})

defineExpose({
  open
})
</script>
