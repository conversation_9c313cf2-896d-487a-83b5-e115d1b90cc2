

## 创建采购订单


**接口地址**:`/admin-api/materials/aog-order/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 17386,
  "supplierId": 1724,
  "orderTime": "",
  "fileUrl": "https://www.iocoder.cn",
  "remark": "你猜",
  "inspectionAddr": "",
  "appearance": "",
  "deliveryNo": "",
  "deliveryType": 1,
  "purchaseOrderNo": "",
  "receiveType": 1,
  "receiveUserId": 13215,
  "qcResult": "",
  "qcUnqualifiedReason": "不好",
  "qcOpinion": "",
  "inspectionResult": "",
  "inspectionOpinion": "",
  "warehouseId": 18970,
  "warehousePartitionId": 24336,
  "signQc": "",
  "signWarehouse": "",
  "signProjectLeader": "",
  "operatorIn": "",
  "operatorReturn": "",
  "signMaterial": "",
  "inType": 1,
  "deliveryFileUrl": "https://www.iocoder.cn",
  "requirementId": 213,
  "requirementNo": "",
  "purchaseOrderId": 24744,
  "inspectionUserId": 31353,
  "inspectionDeptId": 9213,
  "inCategory": "",
  "items": [
    {
      "id": 11614,
      "warehouseId": 11614,
      "name": "李四",
      "remark": "随便"
    }
  ]
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|aogOrderSaveReqVO|管理后台 - ERP 采购订单新增/修改 Request VO|body|true|AogOrderSaveReqVO|AogOrderSaveReqVO|
|&emsp;&emsp;id|编号||true|integer(int64)||
|&emsp;&emsp;supplierId|供应商编号||true|integer(int64)||
|&emsp;&emsp;orderTime|采购时间||true|string(date-time)||
|&emsp;&emsp;fileUrl|附件地址||false|string||
|&emsp;&emsp;remark|备注||false|string||
|&emsp;&emsp;inspectionAddr|检查地址||false|string||
|&emsp;&emsp;appearance|外观检查||false|string||
|&emsp;&emsp;deliveryNo|送货单号||false|string||
|&emsp;&emsp;deliveryType|配送方式||false|string||
|&emsp;&emsp;purchaseOrderNo|采购单号||false|string||
|&emsp;&emsp;receiveType|接收类型||false|string||
|&emsp;&emsp;receiveUserId|接收人||false|string||
|&emsp;&emsp;qcResult|质检结果||false|string||
|&emsp;&emsp;qcUnqualifiedReason|质检不合格原因||false|string||
|&emsp;&emsp;qcOpinion|质检处理意见||false|string||
|&emsp;&emsp;inspectionResult|验收结果||false|string||
|&emsp;&emsp;inspectionOpinion|验收意见||false|string||
|&emsp;&emsp;warehouseId|仓库地址||false|integer(int64)||
|&emsp;&emsp;warehousePartitionId|仓库分区id||false|integer(int64)||
|&emsp;&emsp;signQc|质检员签名||false|string||
|&emsp;&emsp;signWarehouse|仓管员签名||false|string||
|&emsp;&emsp;signProjectLeader|项目负责人签名||false|string||
|&emsp;&emsp;operatorIn|入库操作员||false|string||
|&emsp;&emsp;operatorReturn|退货操作员||false|string||
|&emsp;&emsp;signMaterial|物资部签名||false|string||
|&emsp;&emsp;inType|入库类型||false|string||
|&emsp;&emsp;deliveryFileUrl|到货单据fileUrl||false|string||
|&emsp;&emsp;requirementId|物资需求计划id||false|integer(int64)||
|&emsp;&emsp;requirementNo|物资需求计划no||false|string||
|&emsp;&emsp;purchaseOrderId|采购订单id||false|integer(int64)||
|&emsp;&emsp;inspectionUserId|参检人员||false|string||
|&emsp;&emsp;inspectionDeptId|参检部门||false|string||
|&emsp;&emsp;inCategory|入库操作: 全部入库 让步入库||false|string||
|&emsp;&emsp;items|订单清单列表||false|array|Item|
|&emsp;&emsp;&emsp;&emsp;id|分区编号||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;warehouseId|仓库编号||true|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分区名称||true|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultLong|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||integer(int64)|integer(int64)|
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```