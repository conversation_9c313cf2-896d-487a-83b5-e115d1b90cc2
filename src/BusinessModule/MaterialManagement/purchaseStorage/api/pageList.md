

## 获得采购订单分页


**接口地址**:`/admin-api/materials/aog-order/page`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码，从 1 开始|query|true|integer(int32)||
|pageSize|每页条数，最大值为 100|query|true|integer(int32)||
|no|采购单编号|query|false|string||
|supplierId|供应商编号|query|false|integer(int64)||
|orderTime|采购时间|query|false|array|string|
|remark|备注|query|false|string||
|status|采购状态|query|false|integer(int32)||
|creator|创建者|query|false|string||
|productId|产品编号|query|false|integer(int64)||
|inStatus|入库状态|query|false|integer(int32)||
|returnStatus|退货状态|query|false|integer(int32)||
|inEnable|是否可入库|query|false|boolean||
|returnEnable|是否可退货|query|false|boolean||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPageResultAogOrderRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PageResultAogOrderRespVO|PageResultAogOrderRespVO|
|&emsp;&emsp;list|数据|array|AogOrderRespVO|
|&emsp;&emsp;&emsp;&emsp;id|编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;no|采购单编号|string||
|&emsp;&emsp;&emsp;&emsp;status|采购状态|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;supplierId|供应商编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;supplierName|供应商名称|string||
|&emsp;&emsp;&emsp;&emsp;orderTime|采购时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;totalCount|合计数量|number||
|&emsp;&emsp;&emsp;&emsp;totalProductPrice|合计产品价格，单位：元|number||
|&emsp;&emsp;&emsp;&emsp;fileUrl|附件地址|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;inspectionAddr|检查地址|string||
|&emsp;&emsp;&emsp;&emsp;appearance|外观检查|string||
|&emsp;&emsp;&emsp;&emsp;creator|创建人|string||
|&emsp;&emsp;&emsp;&emsp;creatorName|创建人名称|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;items|订单项列表|array|Item|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|分区编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;warehouseId|仓库编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分区名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;productNames|产品信息|string||
|&emsp;&emsp;&emsp;&emsp;inCount|采购入库数量|number||
|&emsp;&emsp;&emsp;&emsp;returnCount|采购退货数量|number||
|&emsp;&emsp;&emsp;&emsp;deliveryNo|送货单号|string||
|&emsp;&emsp;&emsp;&emsp;deliveryType|配送方式|string||
|&emsp;&emsp;&emsp;&emsp;purchaseOrderNo|采购单号|string||
|&emsp;&emsp;&emsp;&emsp;receiveType|接收类型|string||
|&emsp;&emsp;&emsp;&emsp;receiveUserId|接收人|string||
|&emsp;&emsp;&emsp;&emsp;qcResult|质检结果|string||
|&emsp;&emsp;&emsp;&emsp;qcUnqualifiedReason|质检不合格原因|string||
|&emsp;&emsp;&emsp;&emsp;qcOpinion|质检处理意见|string||
|&emsp;&emsp;&emsp;&emsp;inspectionResult|验收结果|string||
|&emsp;&emsp;&emsp;&emsp;inspectionOpinion|验收意见|string||
|&emsp;&emsp;&emsp;&emsp;warehouseId|仓库地址|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;warehousePartitionId|仓库分区id|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;signQc|质检员签名|string||
|&emsp;&emsp;&emsp;&emsp;signWarehouse|仓管员签名|string||
|&emsp;&emsp;&emsp;&emsp;signProjectLeader|项目负责人签名|string||
|&emsp;&emsp;&emsp;&emsp;operatorIn|入库操作员|string||
|&emsp;&emsp;&emsp;&emsp;operatorReturn|退货操作员|string||
|&emsp;&emsp;&emsp;&emsp;signMaterial|物资部签名|string||
|&emsp;&emsp;&emsp;&emsp;inType|入库类型|string||
|&emsp;&emsp;&emsp;&emsp;deliveryFileUrl|到货单据fileUrl|string||
|&emsp;&emsp;&emsp;&emsp;requirementId|物资需求计划id|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;requirementNo|物资需求计划no|string||
|&emsp;&emsp;&emsp;&emsp;purchaseOrderId|采购订单id|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;inspectionUserId|参检人员|string||
|&emsp;&emsp;&emsp;&emsp;inspectionDeptId|参检部门|string||
|&emsp;&emsp;&emsp;&emsp;inCategory|入库操作: 全部入库 让步入库|string||
|&emsp;&emsp;total|总量|integer(int64)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 17386,
				"no": "XS001",
				"status": 2,
				"supplierId": 1724,
				"supplierName": "芋道",
				"orderTime": "",
				"totalCount": 15663,
				"totalProductPrice": 7127,
				"fileUrl": "https://www.iocoder.cn",
				"remark": "你猜",
				"inspectionAddr": "",
				"appearance": "",
				"creator": "芋道",
				"creatorName": "芋道",
				"createTime": "",
				"items": [
					{
						"id": 11614,
						"warehouseId": 11614,
						"name": "李四",
						"remark": "随便"
					}
				],
				"productNames": "",
				"inCount": 100,
				"returnCount": 100,
				"deliveryNo": "",
				"deliveryType": 1,
				"purchaseOrderNo": "",
				"receiveType": 1,
				"receiveUserId": 13215,
				"qcResult": "",
				"qcUnqualifiedReason": "不好",
				"qcOpinion": "",
				"inspectionResult": "",
				"inspectionOpinion": "",
				"warehouseId": 18970,
				"warehousePartitionId": 24336,
				"signQc": "",
				"signWarehouse": "",
				"signProjectLeader": "",
				"operatorIn": "",
				"operatorReturn": "",
				"signMaterial": "",
				"inType": 1,
				"deliveryFileUrl": "https://www.iocoder.cn",
				"requirementId": 213,
				"requirementNo": "",
				"purchaseOrderId": 24744,
				"inspectionUserId": 31353,
				"inspectionDeptId": 9213,
				"inCategory": ""
			}
		],
		"total": 0
	},
	"msg": ""
}
```