

## 导出采购订单 Excel


**接口地址**:`/admin-api/materials/aog-order/export-excel`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码，从 1 开始|query|true|integer(int32)||
|pageSize|每页条数，最大值为 100|query|true|integer(int32)||
|no|采购单编号|query|false|string||
|supplierId|供应商编号|query|false|integer(int64)||
|orderTime|采购时间|query|false|array|string|
|remark|备注|query|false|string||
|status|采购状态|query|false|integer(int32)||
|creator|创建者|query|false|string||
|productId|产品编号|query|false|integer(int64)||
|inStatus|入库状态|query|false|integer(int32)||
|returnStatus|退货状态|query|false|integer(int32)||
|inEnable|是否可入库|query|false|boolean||
|returnEnable|是否可退货|query|false|boolean||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```