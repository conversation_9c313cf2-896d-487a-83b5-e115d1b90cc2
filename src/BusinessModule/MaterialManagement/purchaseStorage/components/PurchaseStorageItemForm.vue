<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label=" " type="index" align="center" width="40" />
      <el-table-column label="产品" min-width="180" prop="productId">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-select
              v-model="row.productId"
              clearable
              filterable
              placeholder="请选择产品"
              @change="handleProductChange(row)"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="数量" min-width="120" prop="count">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.count`" :rules="formRules.count" class="mb-0px!">
            <el-input-number v-model="row.count" :min="0" :precision="2" placeholder="数量" controls-position="right" style="width: 100%" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- 合格数量 (仅新增时显示) -->
      <el-table-column label="合格数量" min-width="120" prop="inCount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.inCount`" :rules="formRules.inCount" class="mb-0px!">
            <el-input-number v-model="row.inCount" :min="0" :precision="2" placeholder="合格数量" controls-position="right" style="width: 100%" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- 不合格数量 (仅新增时显示) -->
      <el-table-column label="不合格数量" min-width="120" prop="returnCount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.returnCount`" :rules="formRules.returnCount" class="mb-0px!">
            <el-input-number v-model="row.returnCount" :min="0" :precision="2" placeholder="不合格数量" controls-position="right" style="width: 100%" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="120" prop="productPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productPrice`" class="mb-0px!">
            <el-input-number v-model="row.productPrice" :min="0" :precision="2" placeholder="单价" controls-position="right" style="width: 100%" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="120" prop="productUnitName">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-tag>{{ row.productUnitName }}</el-tag>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="库存" min-width="100" prop="stockCount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.stockCount`" class="mb-0px!">
            <el-input-number v-model="row.stockCount" :min="0" :precision="2" controls-position="right" disabled placeholder="库存"  style="width: 100%" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="包装箱号" min-width="120" prop="packageNo">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.packageNo`" class="mb-0px!">
            <el-input v-model="row.packageNo" placeholder="请输入包装箱号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="类型" min-width="100" prop="productType">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productType`" class="mb-0px!">
            <el-select v-model="row.productType" placeholder="请选择类型" style="width: 100%">
              <el-option label="材料" value="1" />
              <el-option label="器械" value="2" />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column v-if="false" label="仓库" min-width="120" prop="warehouseId">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" class="mb-0px!">
            <el-select
              v-model="row.warehouseId"
              placeholder="请选择仓库"
              clearable
              filterable
              style="width: 100%"
              @change="(value) => handleWarehouseChange(row, value)"
            >
              <el-option
                v-for="item in warehouseList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- 新增分区信息列 (仅非新增时显示) -->
      <el-table-column v-if="false" label="分区信息" min-width="120" prop="warehousePartitionId">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehousePartitionId`" class="mb-0px!">
            <el-select
              v-model="row.warehousePartitionId"
              placeholder="请选择分区"
              clearable
              filterable
              style="width: 100%"
              :disabled="!row.warehouseId"
            >
              <el-option
                v-for="item in getPartitionOptions(row.warehouseId)"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150" prop="remark">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60" v-if="!disabled">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加入库物料</el-button>
  </el-row>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import type { Item as PurchaseStorageItem } from '@/api/material/purchaseStorage'
import { WarehouseApi } from '@/api/erp/stock/warehouse'
import { ProductApi } from '@/api/erp/product/product'

const props = defineProps<{
  items: PurchaseStorageItem[]
  disabled: boolean
  isAdd: boolean
}>()

const formLoading = ref(false)
const formData = ref<PurchaseStorageItem[]>([])
const warehouseList = ref([])
const productList = ref<Array<{id: number, name: string, unitId?: number, unitName?: string, type?: string}>>([])
// 分区缓存 {仓库ID: 分区列表}
const partitionCache = ref<Record<number, Array<{id: number, name: string}>>>({})

// 获取仓库列表
const getWarehouseList = async () => {
    const res = await WarehouseApi.getWarehouseSimpleList()
    warehouseList.value  = res
}

const formRules = reactive({
  productId: [{ required: true, message: '请选择产品', trigger: 'change' }],
  count: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  warehouseId: props.isAdd ? [] : [{ required: true, message: '请选择仓库', trigger: 'change' }],
  inCount: props.isAdd ? [{ required: true, message: '请输入合格数量', trigger: 'blur' }] : [],
  returnCount: props.isAdd ? [{ required: true, message: '请输入不合格数量', trigger: 'blur' }] : []
})
const formRef = ref<FormInstance>()

// 获取分区选项
const getPartitionOptions = (warehouseId: number) => {
  return partitionCache.value[warehouseId] || []
}

// 仓库变更处理
const handleWarehouseChange = async (row: PurchaseStorageItem, warehouseId: number) => {
  // 清空分区选择
  row.warehousePartitionId = undefined
  
  // 如果仓库ID有效则加载分区
  if (warehouseId) {
    await loadPartitions(warehouseId)
  }
}

// 加载分区
const loadPartitions = async (warehouseId: number) => {
  // 如果已缓存则跳过
  if (partitionCache.value[warehouseId]) return
  
  try {
    const partitions = await WarehouseApi.listPartition(warehouseId)
    partitionCache.value = {
      ...partitionCache.value,
      [warehouseId]: partitions
    }
  } catch (err) {
    console.error(`加载仓库 ${warehouseId} 的分区失败:`, err)
    partitionCache.value[warehouseId] = []
  }
}

/** 初始化数据 */
watch(
  () => props.items,
  (val) => {
    formData.value = val || []
  },
  { immediate: true }
)

/** 添加物料项 */
const handleAdd = () => {
  formData.value.push({
    id: undefined,
    productId: undefined,
    productUnitId: undefined,
    productPrice: undefined,
    count: undefined,
    warehouseId: undefined,
    warehousePartitionId: undefined, // 新增分区字段
    remark: '',
    inCount: undefined,
    returnCount: undefined,
    packageNo: '',
    purchaseOrderItemId: undefined,
    requirementItemId: undefined,
    productType: undefined,
    purchaseCount: undefined
  })
}

/** 删除物料项 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单验证 */
const validate = () => {
  return formRef.value?.validate()
}
defineExpose({ validate })

// 产品变更处理
const handleProductChange = (row: PurchaseStorageItem) => {
  const product = productList.value.find(item => item.id === row.productId)
  if (product) {
    row.productUnitId = product.unitId
    row.productUnitName = product.unitName
    row.productType = product.type
  }
}

// 初始化时获取仓库列表和产品列表
onMounted(async () => {
  await getWarehouseList()
  try {
    const res = await ProductApi.getProductSimpleList()
    productList.value = res.map(item => ({
      id: item.id,
      name: item.name,
      unitId: item.unitId,
      unitName: item.unitName,
      type: item.type
    }))
  } catch (err) {
    console.error('获取产品列表失败:', err)
    productList.value = []
  }
  
  // 加载现有行的仓库分区
  formData.value.forEach(row => {
    if (row.warehouseId) {
      loadPartitions(row.warehouseId)
    }
  })
  
  if (formData.value.length === 0 && !props.disabled) {
    handleAdd()
  }
})

</script>
