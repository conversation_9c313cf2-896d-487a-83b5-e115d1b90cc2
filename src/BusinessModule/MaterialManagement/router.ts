import { Layout } from '@/utils/routerHelper'

// const { t } = useI18n()

export default  {
    path: '/material',
    component: Layout,
    name: 'material',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'material/inspection',
        name: 'ArrivalInspectionManagement',
        meta: {
          title: '到货验收管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material/inspection'
        },
        component: () => import('@/BusinessModule/MaterialManagement/ArrivalInspectionManagement/index.vue')
      },
      {
        path: '/material/ArrivalInspectionManagementEdit',
        name: 'ArrivalInspectionManagementEdit',
        meta: {
          title: '到货验收修改',
          noCache: true,
          hidden: true,
          activeMenu: 'material/ArrivalInspectionManagementEdit'
        },
        component: () => import('@/BusinessModule/MaterialManagement/ArrivalInspectionManagement/mirror.vue')
      },
      {
        path: '/material/ArrivalInspectionManagementDetail',
        name: 'ArrivalInspectionManagementDetail',
        meta: {
          title: '到货验收详情',
          noCache: true,
          hidden: true,
          activeMenu: 'material/ArrivalInspectionManagementDetail'
        },
        component: () => import('@/BusinessModule/MaterialManagement/ArrivalInspectionManagement/mirror.vue')
      },

      {
        path: 'material/purchasePlanApproval',
        name: 'PurchasePlanApprovalManagement',
        meta: {
          title: '采购计划报审管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material/purchasePlanApproval'
        },
        component: () => import('@/BusinessModule/MaterialManagement/PurchasePlanApprovalManagement/index.vue')
      },
    ]
  }
