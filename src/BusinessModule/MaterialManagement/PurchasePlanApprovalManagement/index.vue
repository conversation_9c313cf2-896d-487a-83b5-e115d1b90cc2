<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="85px"
    >
      <el-form-item label="报表编号" prop="planCode">
        <el-input
          v-model="queryParams.planCode"
          class="!w-240px"
          clearable
          placeholder="请输入报表编号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报表类型" prop="planType">
        <el-select
          v-model="queryParams.planType"
          class="!w-240px"
          clearable
          placeholder="请选择报表类型"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_PLAN_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报表状态" prop="planStatus">
        <el-select
          v-model="queryParams.planStatus"
          class="!w-240px"
          clearable
          placeholder="请选择报表状态"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_PLAN_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          v-hasPermi="['material:plan-review:create']"
          plain
          type="primary"
          @click="openForm('create')"
        >
          <Icon class="mr-5px" icon="ep:plus" />
          新增报审
        </el-button>
        <el-button
          v-hasPermi="['material:plan-review:export']"
          :loading="exportLoading"
          plain
          type="success"
          @click="handleExport"
        >
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column min-width="150" align="center" label="报表编号" prop="planCode" />
      <el-table-column min-width="150" align="center" label="报表类型" prop="planType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_PLAN_TYPE" :value="scope.row.planType" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="报表名称" prop="planName" />
      <el-table-column min-width="150" align="center" label="报表状态" prop="planStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_PLAN_STATUS" :value="scope.row.planStatus" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="工程编号" prop="projectCode" />
      <el-table-column min-width="150" align="center" label="工程名称" prop="projectName" />
      <el-table-column min-width="150" align="center" label="供应商编号" prop="supplierId" />
      <el-table-column min-width="150" align="center" label="供应商名称" prop="supplierName" />
      <el-table-column min-width="150" align="center" label="内容类型" prop="contentType" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_CONTENT_TYPE" :value="scope.row.contentType" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="内容编号" prop="contentCode" />
      <!-- <el-table-column  min-width="150"  align="center" label="附件地址" prop="fileUrl" /> -->
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="创建时间"
        prop="createTime"
        width="180"
      />
      <el-table-column min-width="150" align="center" label="备注" prop="remark" />
      <el-table-column min-width="150" :width="260" align="center" label="操作" fixed="right">
        <template #default="scope">

          <el-button
            v-hasPermi="['material:plan-review:delete']"
            link
            type="primary"
            @click="downloadPlanTemplate(scope.row)"
          >
            生成报审
          </el-button>
          
          <el-button
            v-hasPermi="['material:plan-review:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['material:plan-review:detail']"
            link
            type="primary"
            @click="openDetail(scope.row.id)"
          >
            详情
          </el-button>
          <el-button
            v-hasPermi="['material:plan-review:delete']"
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改/详情 -->
  <PlanReviewForm
    ref="formRef"
    :model-value="dialogVisible"
    :mode="dialogType"
    @update:model-value="dialogVisible = !!$event"
    @success="getList"
  />
</template>

<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions, getDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as PlanReviewApi from '@/api/material/plan-review'
import PlanReviewForm from './PlanReviewForm.vue'
import * as docxMaker from '../../../utils/plandocxdownload'

defineOptions({ name: 'MaterialPlanReview' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 表单弹窗的显示状态
const dialogType = ref('edit') // 表单弹窗的类型
const previewVisable = ref(true)

const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  planCode: '',
  planType: undefined,
  planName: '',
  planStatus: undefined,
  projectCode: '',
  projectName: '',
  supplierId: undefined,
  supplierName: '',
  contentType: undefined,
  contentCode: '',
  fileUrl: '',
  createTime: [],
  remark: ''
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询计划报审列表 */
const getList = async () => {
  loading.value = true
  try {
    // 对queryParams 清除空值
    Object.keys(queryParams).forEach((key) => {
      if (queryParams[key] === '' || queryParams[key] === undefined) {
        delete queryParams[key]
      }
    })
    const data = await PlanReviewApi.getPlanReviewPage(queryParams)
    list.value = data.list
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  dialogType.value = 'edit'
  formRef.value.open(type, id)
}

/** 详情操作 */
const openDetail = (id: number) => {
  dialogType.value = 'readonly'
  formRef.value.open('', id)
}
const downloadPlanTemplate = (row) => docxMaker.downloadPlanTemplate(row,row.id+'计划申报表')
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PlanReviewApi.deletePlanReview(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PlanReviewApi.exportPlanReview(queryParams)
    download.excel(data, '计划报审列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
