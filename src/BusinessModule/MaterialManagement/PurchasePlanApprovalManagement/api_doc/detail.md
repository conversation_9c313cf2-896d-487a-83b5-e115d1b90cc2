

## 获得计划报审


**接口地址**:`/materials/plan-review/get`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|编号|query|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPlanReviewRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PlanReviewRespVO|PlanReviewRespVO|
|&emsp;&emsp;id|主键ID|integer(int64)||
|&emsp;&emsp;planCode|报表编号|string||
|&emsp;&emsp;planType|报表类型|string||
|&emsp;&emsp;planName|报表名称|string||
|&emsp;&emsp;planStatus|报表状态|string||
|&emsp;&emsp;projectCode|工程编号|string||
|&emsp;&emsp;projectName|工程名称|string||
|&emsp;&emsp;supplierId|供应商编号|integer(int64)||
|&emsp;&emsp;supplierName|供应商名称|string||
|&emsp;&emsp;contentType|内容类型|string||
|&emsp;&emsp;contentCode|内容编号|string||
|&emsp;&emsp;fileUrl|附件地址|string||
|&emsp;&emsp;fileUrlList||array|string|
|&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;remark|备注|string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"id": 28684,
		"planCode": "",
		"planType": 1,
		"planName": "张三",
		"planStatus": 2,
		"projectCode": "",
		"projectName": "张三",
		"supplierId": 15504,
		"supplierName": "张三",
		"contentType": 2,
		"contentCode": "",
		"fileUrl": "https://www.iocoder.cn",
		"fileUrlList": [],
		"createTime": "",
		"remark": "你说的对"
	},
	"msg": ""
}
```