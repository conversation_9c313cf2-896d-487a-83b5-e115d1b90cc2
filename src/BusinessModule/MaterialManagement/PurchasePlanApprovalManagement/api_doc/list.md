

## 获得计划报审分页


**接口地址**:`/materials/plan-review/page`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码，从 1 开始|query|true|integer(int32)||
|pageSize|每页条数，最大值为 100|query|true|integer(int32)||
|planCode|报表编号|query|false|string||
|planType|报表类型|query|false|string||
|planName|报表名称|query|false|string||
|planStatus|报表状态|query|false|string||
|projectCode|工程编号|query|false|string||
|projectName|工程名称|query|false|string||
|supplierId|供应商编号|query|false|integer(int64)||
|supplierName|供应商名称|query|false|string||
|contentType|内容类型|query|false|string||
|contentCode|内容编号|query|false|string||
|fileUrl|附件地址|query|false|string||
|createTime|创建时间|query|false|array|string|
|remark|备注|query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPageResultPlanReviewRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PageResultPlanReviewRespVO|PageResultPlanReviewRespVO|
|&emsp;&emsp;list|数据|array|PlanReviewRespVO|
|&emsp;&emsp;&emsp;&emsp;id|主键ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;planCode|报表编号|string||
|&emsp;&emsp;&emsp;&emsp;planType|报表类型|string||
|&emsp;&emsp;&emsp;&emsp;planName|报表名称|string||
|&emsp;&emsp;&emsp;&emsp;planStatus|报表状态|string||
|&emsp;&emsp;&emsp;&emsp;projectCode|工程编号|string||
|&emsp;&emsp;&emsp;&emsp;projectName|工程名称|string||
|&emsp;&emsp;&emsp;&emsp;supplierId|供应商编号|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;supplierName|供应商名称|string||
|&emsp;&emsp;&emsp;&emsp;contentType|内容类型|string||
|&emsp;&emsp;&emsp;&emsp;contentCode|内容编号|string||
|&emsp;&emsp;&emsp;&emsp;fileUrl|附件地址|string||
|&emsp;&emsp;&emsp;&emsp;fileUrlList||array|string|
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;total|总量|integer(int64)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"id": 28684,
				"planCode": "",
				"planType": 1,
				"planName": "张三",
				"planStatus": 2,
				"projectCode": "",
				"projectName": "张三",
				"supplierId": 15504,
				"supplierName": "张三",
				"contentType": 2,
				"contentCode": "",
				"fileUrl": "https://www.iocoder.cn",
				"fileUrlList": [],
				"createTime": "",
				"remark": "你说的对"
			}
		],
		"total": 0
	},
	"msg": ""
}
```