

## 导出计划报审 Excel


**接口地址**:`/materials/plan-review/export-excel`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码，从 1 开始|query|true|integer(int32)||
|pageSize|每页条数，最大值为 100|query|true|integer(int32)||
|planCode|报表编号|query|false|string||
|planType|报表类型|query|false|string||
|planName|报表名称|query|false|string||
|planStatus|报表状态|query|false|string||
|projectCode|工程编号|query|false|string||
|projectName|工程名称|query|false|string||
|supplierId|供应商编号|query|false|integer(int64)||
|supplierName|供应商名称|query|false|string||
|contentType|内容类型|query|false|string||
|contentCode|内容编号|query|false|string||
|fileUrl|附件地址|query|false|string||
|createTime|创建时间|query|false|array|string|
|remark|备注|query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||


**响应参数**:


暂无


**响应示例**:
```javascript

```