

## 创建计划报审


**接口地址**:`/materials/plan-review/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 28684,
  "planCode": "",
  "planType": "PLAN",
  "planName": "张三",
  "planStatus": 0,
  "projectCode": "",
  "projectName": "张三",
  "supplierId": 15504,
  "contentType": 2,
  "contentCode": "",
  "fileUrl": "https://www.iocoder.cn",
  "fileUrlList": [],
  "remark": "你说的对"
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|planReviewSaveReqVO|管理后台 - 计划报审新增/修改 Request VO|body|true|PlanReviewSaveReqVO|PlanReviewSaveReqVO|
|&emsp;&emsp;id|主键ID||true|integer(int64)||
|&emsp;&emsp;planCode|报表编号||true|string||
|&emsp;&emsp;planType|报表类型||true|string||
|&emsp;&emsp;planName|报表名称||true|string||
|&emsp;&emsp;planStatus|报表状态||false|string||
|&emsp;&emsp;projectCode|工程编号||true|string||
|&emsp;&emsp;projectName|工程名称||true|string||
|&emsp;&emsp;supplierId|供应商编号||true|integer(int64)||
|&emsp;&emsp;contentType|内容类型||true|string||
|&emsp;&emsp;contentCode|内容编号||true|string||
|&emsp;&emsp;fileUrl|附件地址||false|string||
|&emsp;&emsp;fileUrlList|||false|array|string|
|&emsp;&emsp;remark|备注||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultLong|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||integer(int64)|integer(int64)|
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": 0,
	"msg": ""
}
```