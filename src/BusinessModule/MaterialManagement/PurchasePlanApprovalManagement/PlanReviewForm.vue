<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="close">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="mode === 'readonly'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报表编号" prop="planCode">
            <el-input v-model="form.planCode" placeholder="请输入报表编号" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报表类型" prop="planType">
            <el-select
              v-model="form.planType"
              placeholder="请选择报表类型"
              class="!w-240px"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_PLAN_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报表名称" prop="planName">
            <el-input
              v-model="form.planName"
              placeholder="请输入报表名称"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报表状态" prop="planStatus">
            <el-select
              v-model="form.planStatus"
              placeholder="请选择报表状态"
              class="!w-240px"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_PLAN_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工程编号" prop="projectCode">
            <el-input v-model="form.projectCode" placeholder="请输入工程编号" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入工程名称" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供应商" prop="supplierId">
            <el-select
              v-model="form.supplierId"
              placeholder="请选择供应商"
              class="!w-240px"
              filterable
              clearable
            >
              <el-option
                v-for="supplier in supplierList"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="内容类型" prop="contentType">
            <el-select
              v-model="form.contentType"
              placeholder="请选择内容类型"
              class="!w-240px"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_CONTENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="内容编号" prop="contentCode">
            <el-select
              v-model="form.contentCode"
              filterable
              remote
              clearable
              :remote-method="remoteSearch"
              :loading="contentCodeLoading"
              placeholder="请输入内容编号搜索"
              class="!w-240px"
            >
              <el-option
                v-for="item in contentCodeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="承包单位" prop="contractorOrg">
            <el-input v-model="form.contractorOrg" placeholder="请输入承包单位" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="承包单位部门" prop="contractorDept">
            <el-input v-model="form.contractorDept" placeholder="请输入承包单位部门" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设单位" prop="architectOrg">
            <el-input v-model="form.architectOrg" placeholder="请输入建设单位" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建设单位部门" prop="architectDept">
            <el-input v-model="form.architectDept" placeholder="请输入建设单位部门" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目监理机构" prop="supervisorOrg">
            <el-input v-model="form.supervisorOrg" placeholder="请输入项目监理机构" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目监理机构部门" prop="supervisorDept">
            <el-input v-model="form.supervisorDept" placeholder="请输入项目监理机构部门" class="!w-240px" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="附件" prop="fileUrl">
            <UploadFile
              :is-show-tip="false"
              :model-value="form.fileUrlList"
              @update:model-value="form.fileUrlList = $event || []"
            />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="close">取 消</el-button>
      <el-button v-if="mode !== 'readonly'" type="primary" @click="submitForm"> 确 定 </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { PurchaseOrderVO } from '@/api/erp/purchase/order'
import { ElMessage } from 'element-plus'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import * as PlanReviewApi from '@/api/material/plan-review/index'
import * as InspectionApi from '@/api/material/inspection/index'
import type { PlanReviewSaveReqVO } from '@/api/material/plan-review'
import { PurchaseOrderApi } from '@/api/erp/purchase/order/index'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  id: {
    type: Number,
    default: undefined
  },
  mode: {
    type: String,
    default: 'edit', // 'edit' | 'readonly'
    validator: (value: string) => ['edit', 'readonly'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const supplierList = ref<Array<{ id: number; name: string }>>([])


const detailId = ref<number | undefined>(undefined)

// 表单初始值
const form = reactive<PlanReviewSaveReqVO>({
  planCode: '',
  planType: '',
  planName: '',
  planStatus: '',
  projectCode: '',
  projectName: '',
  supplierId: 0,
  supplierName: '',
  contentType: '',
  contentCode: '',
  fileUrl: '',
  fileUrlList: [],
  remark: '',
  contractorOrg: '',
  contractorDept: '',
  architectOrg: '',
  architectDept: '',
  supervisorOrg: '',
  supervisorDept: ''
})

const contentCodeLoading = ref(false)
const contentCodeOptions = ref<Array<{value: string, label: string}>>([])

const remoteSearch = async (query: string) => {
  if (query===undefined) {
    contentCodeOptions.value = []
    return
  }
  contentCodeLoading.value = true
  try {
    const res = await PurchaseOrderApi.getPurchaseOrderPage({
      pageNo: 1,
      pageSize: 20,
      no: query
    })
    contentCodeOptions.value = res.list.map((item: PurchaseOrderVO) => ({
      value: item.no,
      label: item.no
    }))
  } catch (err) {
    console.error('搜索采购订单失败:', err)
    contentCodeOptions.value = []
  } finally {
    contentCodeLoading.value = false
  }
}

const rules = reactive({
  planCode: [{ required: true, message: '报表编号不能为空', trigger: 'blur' }],
  planType: [{ required: true, message: '报表类型不能为空', trigger: 'change' }],
  planName: [{ required: true, message: '报表名称不能为空', trigger: 'blur' }],
  projectCode: [{ required: true, message: '工程编号不能为空', trigger: 'blur' }],
  projectName: [{ required: true, message: '工程名称不能为空', trigger: 'blur' }],
  supplierId: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  contentType: [{ required: true, message: '请选择内容类型', trigger: 'change' }],
  // contentCode: [{ required: true, message: '内容编号不能为空', trigger: 'blur' }],
  contractorOrg: [{ required: false, message: '请输入承包单位', trigger: 'blur' }],
  contractorDept: [{ required: false, message: '请输入承包单位部门', trigger: 'blur' }],
  architectOrg: [{ required: false, message: '请输入建设单位', trigger: 'blur' }],
  architectDept: [{ required: false, message: '请输入建设单位部门', trigger: 'blur' }],
  supervisorOrg: [{ required: false, message: '请输入项目监理机构', trigger: 'blur' }],
  supervisorDept: [{ required: false, message: '请输入项目监理机构部门', trigger: 'blur' }]
})

// 获取供应商列表
const getSupplierList = async () => {
  try {
    const res = await InspectionApi.getSupplierSimpleList()
    supplierList.value = res.map((item: { id: number; name: string }) => ({
      id: item.id,
      name: item.name
    }))
  } catch (err) {
    console.error('获取供应商列表失败:', err)
  }
}

const open = async (type: string, id?: number) => {
  await getSupplierList()
  dialogVisible.value = true
  detailId.value = props.id || id
  if (detailId.value) {
    // 编辑模式，获取详情
    const res = await PlanReviewApi.getPlanReviewDetail(detailId.value)
    Object.assign(form, res)
  }
}

// 关闭弹窗
const close = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
  detailId.value = undefined
  emit('update:modelValue', false)
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()

    const submitData: PlanReviewSaveReqVO = {
      ...form,
      id: props.id || detailId.value
    }

    if (submitData.id) {
      // 编辑
      await PlanReviewApi.updatePlanReview(submitData)
    } else {
      // 新增
      await PlanReviewApi.createPlanReview(submitData)
    }

    ElMessage.success('操作成功')
    close()
    emit('success')
  } catch (err: any) {
    console.error('表单提交错误:', err)
    ElMessage.error(err.message || '操作失败，请重试')
  }
}

remoteSearch('')
defineExpose({
  open
})
</script>
