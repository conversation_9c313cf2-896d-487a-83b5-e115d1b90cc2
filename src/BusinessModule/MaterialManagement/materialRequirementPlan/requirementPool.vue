<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="85px"
    >
      <el-form-item label="产品类型" prop="productType">
        <el-select
          v-model="queryParams.productType"
          class="!w-240px"
          clearable
          placeholder="请选择产品类型"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_PRODUCT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          class="!w-240px"
          clearable
          placeholder="请输入产品名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button
          v-hasPermi="['material:requirement-plan:export']"
          :loading="exportLoading"
          plain
          type="success"
          @click="handleExport"
        >
          <Icon class="mr-5px" icon="ep:download" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column min-width="150" align="center" label="产品名称" prop="productName" />
      <el-table-column min-width="150" align="center" label="产品类型" prop="productType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_PRODUCT_TYPE" :value="scope.row.productType" />
        </template>
      </el-table-column>
      <el-table-column min-width="150" align="center" label="产品分类" prop="categoryName" />
      <el-table-column min-width="150" align="center" label="规格" prop="productStandard" />
      <el-table-column min-width="150" align="center" label="型号" prop="productModel" />
      <el-table-column min-width="150" align="center" label="生产厂家" prop="productManufacturer" />
      <el-table-column min-width="150" align="center" label="单位" prop="unitName" />
      <el-table-column min-width="150" align="center" label="总数量" prop="productCount" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as RequirementPlanApi from '@/api/material/requirement-plan'

defineOptions({ name: 'MaterialRequirementPool' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  productType: undefined,
  productName: ''
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询项目需求池汇总列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RequirementPlanApi.getRequirementPlanSummary(queryParams)
    list.value = data.list
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RequirementPlanApi.exportRequirementPlan(queryParams)
    download.excel(data, '项目需求池汇总.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
