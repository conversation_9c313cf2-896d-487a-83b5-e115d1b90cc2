<template>
  <el-dialog :title="title" v-model="dialogVisible" width="80%" append-to-body @close="close">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="mode === 'readonly'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划编号" prop="no">
            <el-input v-model="form.no" placeholder="请输入需用计划编号" class="!w-240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划类型" prop="requirementType">
            <el-select v-model="form.requirementType" placeholder="请选择计划类型" class="!w-240px">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_REQUIREMENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="编制员人" prop="establishUserId">
            <UserSelector
              ref="userSelectRef"
              v-model="form.establishUserId "
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编制日期" prop="establishTime">
            <el-date-picker
              v-model="form.establishTime"
              disabled
              type="date"
              placeholder="请选择编制日期"
              value-format="YYYY-MM-DD"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="计划描述" prop="desc">
            <el-input
              v-model="form.desc"
              type="textarea"
              :rows="3"
              placeholder="请输入计划描述"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件" prop="fileUrl">
            <UploadFile
              :is-show-tip="false"
              :model-value="form.fileUrlList"
              @update:model-value="form.fileUrlList = $event || []"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              class="!w-500px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <ContentWrap>
        <el-tabs v-model="subTabsName" class="-mt-15px -mb-10px">
          <el-tab-pane label="物资项清单" name="item">
            <RequirementPlanItemForm
              ref="itemFormRef"
              :items="form.items"
              :disabled="mode === 'readonly'"
            />
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>
    </el-form>

    <template #footer>
      <el-button @click="close">取 消</el-button>
      <el-button v-if="mode !== 'readonly'" type="primary" @click="submitForm"> 确 定 </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DICT_TYPE, getDictOptions, getDictLabel } from '@/utils/dict'
import * as RequirementPlanApi from '@/api/material/requirement-plan/index'
import RequirementPlanItemForm from './components/RequirementPlanItemForm.vue'
import UserSelector from '@/components/UserSelectForm/UserSelector.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  id: {
    type: Number,
    default: undefined
  },
  mode: {
    type: String,
    default: 'edit', // 'edit' | 'readonly'
    validator: (value: string) => ['edit', 'readonly'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const dialogVisible = ref(false)
const formRef = ref()
const itemFormRef = ref()
const userSelectRef = ref()

// 表单初始值
const form = reactive<RequirementPlanApi.RequirementPlanSaveReqVO>({
  no: '',
  requirementType: '',
  establishDeptId: '',
  establishUserId: '',
  establishDate: '',
  desc: '',
  fileUrl: '',
  fileUrlList: [],
  remark: '',
  items: []
})

const rules = reactive({
  no: [{ required: true, message: '需用计划编号不能为空', trigger: 'blur' }],
  requirementType: [{ required: true, message: '需用类型不能为空', trigger: 'change' }],
  establishUserId: [{ required: true, message: '请选择编制员人', trigger: 'change' }],
  establishDate: [{ required: false, message: '请选择编制日期', trigger: 'change' }],
  desc: [{ required: true, message: '计划描述不能为空', trigger: 'blur' }],
  items: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value || value.length === 0) {
          callback(new Error('至少需要一个物资项'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
})

const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  if (props.id || id) {
    // 编辑模式，获取详情
    const res = await RequirementPlanApi.getRequirementPlanDetail(props.id || id!)
    Object.assign(form, res)
  }
}

// 处理用户选择
const handleUserSelect = (id: number, userList: any[]) => {
  if (userList.length > 0) {
    form.establishUserId = userList[0].id
  }
}

// 关闭弹窗
const close = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
  emit('update:modelValue', false)
}

const subTabsName = ref('item')

// 提交表单
const submitForm = async () => {
  try {
    await Promise.all([formRef.value.validate(), itemFormRef.value?.validate()])

    const submitData = {
      ...form,
      id: props.id
    }

    if (submitData.id) {
      // 编辑
      await RequirementPlanApi.updateRequirementPlan(submitData)
    } else {
      // 新增
      await RequirementPlanApi.createRequirementPlan(submitData)
    }

    ElMessage.success('操作成功')
    close()
    emit('success')
  } catch (err: any) {
    console.error('表单提交错误:', err)
    ElMessage.error(err.message || '操作失败，请重试')
  }
}

defineExpose({
  open
})
</script>
