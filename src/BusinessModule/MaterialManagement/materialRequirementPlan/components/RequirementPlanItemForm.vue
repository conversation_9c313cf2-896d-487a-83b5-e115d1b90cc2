<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" align="center" width="60" />
      <el-table-column label="物资名称" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productId`" :rules="formRules.productId" class="mb-0px!">
            <el-select
              v-model="row.productId"
              clearable
              filterable
              placeholder="请选择产品"
              @change="handleProductChange(row)"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物资分类" min-width="120">
        <template #default="{ row }">
          <el-form-item :prop="`${$index}.productType`" class="mb-0px!">
            <el-select
              v-model="row.productType"
              placeholder="请选择物资分类"
              :disabled="disabled"
              class="!w-100%"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MATERIAL_PRODUCT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column label="生产厂家" min-width="150">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-tag>{{ row.productManufacturer }}</el-tag>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="规格型号" min-width="150">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-tag>{{ row.productSpec }}</el-tag>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="数量" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`${$index}.productCount`"
            :rules="formRules.productCount"
            class="mb-0px!"
          >
            <el-input-number
              v-model="row.productCount"
              controls-position="right"
              :min="0.001"
              :precision="3"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="120">
        <template #default="{ row }">
          <el-form-item class="mb-0px!">
            <el-tag>{{ row.productUnitName }}</el-tag>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="用于工程部位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.usedForProjectPart`" class="mb-0px!">
            <el-input v-model="row.usedForProjectPart" placeholder="请输入工程部位" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="首批到场日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.firstBatchArrivalTime`" class="mb-0px!">
            <el-date-picker
              v-model="row.firstBatchArrivalTime"
              type="date"
              placeholder="选择日期"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="末批到场日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.finalBatchArrivalTime`" class="mb-0px!">
            <el-date-picker
              v-model="row.finalBatchArrivalTime"
              type="date"
              placeholder="选择日期"
              class="!w-100%"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="需求描述" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.desc`" class="mb-0px!">
            <el-input v-model="row.desc" placeholder="请输入描述" />
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="80" fixed="right">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link type="delete">—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加物资项</el-button>
  </el-row>
</template>

<script setup lang="ts">
import { ProductApi, ProductVO } from '@/api/erp/product/product'
import { DICT_TYPE, getDictLabel, getDictOptions } from '@/utils/dict'
import type { FormInstance } from 'element-plus'

const props = defineProps<{
  items: any[]
  disabled: boolean
}>()

const formRef = ref<FormInstance>()
const formData = ref<any[]>([])
const productList = ref<ProductVO[]>([])

const formRules = reactive({
  productId: [{ required: true, message: '请选择产品', trigger: 'change' }],
  productCount: [{ required: true, message: '请输入数量', trigger: 'blur' }]
})

// 初始化数据
watch(
  () => props.items,
  (val) => {
    formData.value = val || []
  },
  { immediate: true }
)

// 添加物资项
const handleAdd = () => {
  const today = new Date()
  const formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

  formData.value.push({
    id: undefined,
    productId: undefined,
    productType: undefined,
    productName: undefined,
    productUnitId: undefined,
    productUnitName: undefined,
    productManufacturer: undefined,
    productSpec: undefined,
    productCount: 1,
    firstBatchArrivalTime: formattedDate,
    finalBatchArrivalTime: undefined,
    usedForProjectPart: undefined,
    desc: undefined,
    fileUrl: undefined,
    fileUrlList: [],
    remark: undefined
  })
}

// 删除物资项
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

// 产品变更处理
const handleProductChange = (row: any) => {
  const product = productList.value.find((item) => item.id === row.productId)
  if (product) {
    // 产品类型保持原值，不自动更新，由用户选择
    row.productName = product.name
    row.productUnitId = product.unitId
    row.productUnitName = product.unitName
    row.productManufacturer = product.manufacturer
    row.productSpec = product.model
  }
}

// 表单验证
const validate = () => {
  return formRef.value?.validate()
}

// 初始化产品列表
onMounted(async () => {
  try {
    productList.value = await ProductApi.getProductSimpleList()
  } catch (err) {
    console.error('获取产品列表失败:', err)
    productList.value = []
  }
  if (formData.value.length === 0 && !props.disabled) {
    handleAdd()
  }
})

defineExpose({ validate })
</script>
