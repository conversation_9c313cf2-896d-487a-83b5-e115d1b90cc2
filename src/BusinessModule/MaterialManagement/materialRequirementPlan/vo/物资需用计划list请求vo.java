package cn.iocoder.yudao.module.materials.controller.admin.requirement.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物资-需用计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RequirementPlanPageReqVO extends PageParam {

    @Schema(description = "需用计划编号")
    private String no;

    @Schema(description = "需用类型", example = "2")
    private String requirementType;

    @Schema(description = "编制单位", example = "350")
    private String establishDeptId;

    @Schema(description = "编制人", example = "24417")
    private String establishUserId;

    @Schema(description = "计划描述")
    private String desc;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

}