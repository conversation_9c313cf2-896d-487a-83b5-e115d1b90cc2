package cn.iocoder.yudao.module.materials.controller.admin.requirement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import jakarta.validation.constraints.*;
import cn.iocoder.yudao.module.materials.dal.dataobject.requirement.RequirementPlanItemsDO;

@Schema(description = "管理后台 - 物资-需用计划新增/修改 Request VO")
@Data
public class RequirementPlanSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29109")
    private Long id;

    @Schema(description = "需用计划编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "需用计划编号不能为空")
    private String no;

    @Schema(description = "需用类型", example = "2")
    private String requirementType;

    @Schema(description = "编制单位", example = "350")
    private String establishDeptId;

    @Schema(description = "编制人", example = "24417")
    private String establishUserId;

    @Schema(description = "计划描述")
    private String desc;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    private String fileUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "物资 物资-需用计划-单项列表")
    private List<Item> items;

    @Data
    public static class Item {
        @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21627")
        private Long id;

        @Schema(description = "需要计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10329")
        @NotNull(message = "需要计划ID不能为空")
        private Long requirementId;

        @Schema(description = "产品类型,从数据字典取:材料、器具", example = "1")
        private String productType;

        @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "25678")
        @NotNull(message = "产品编号不能为空")
        private Long productId;

        @Schema(description = "产品单位单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "20124")
        @NotNull(message = "产品单位单位不能为空")
        private Long productUnitId;

        @Schema(description = "产品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1857")
        @NotNull(message = "产品数量不能为空")
        private BigDecimal productCount;

        @Schema(description = "计划首批到场日期", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "计划首批到场日期不能为空")
        private LocalDateTime firstBatchArrivalTime;

        @Schema(description = "计划末批到场日期", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "计划末批到场日期不能为空")
        private LocalDateTime finalBatchArrivalTime;

        @Schema(description = "用于工程部位")
        private String usedForProjectPart;

        @Schema(description = "描述")
        private String desc;

        @Schema(description = "附件地址", example = "https://www.iocoder.cn")
        private String fileUrl;

        @Schema(description = "备注", example = "随便")
        private String remark;
    }

}