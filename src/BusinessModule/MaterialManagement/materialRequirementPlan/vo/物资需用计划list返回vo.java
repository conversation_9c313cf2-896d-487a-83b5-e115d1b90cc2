package cn.iocoder.yudao.module.materials.controller.admin.requirement.vo;

import cn.iocoder.yudao.module.materials.dal.dataobject.product.ProductDO;
import cn.iocoder.yudao.module.materials.dal.dataobject.product.ProductWithoutIdDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.*;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 物资-需用计划 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RequirementPlanRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29109")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "需用计划编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("需用计划编号")
    private String no;

    @Schema(description = "需用类型", example = "2")
    @ExcelProperty("需用类型")
    private String requirementType;

    @Schema(description = "编制单位", example = "350")
    @ExcelProperty("编制单位")
    private String establishDeptId;

    @Schema(description = "编制人", example = "24417")
    @ExcelProperty("编制人")
    private String establishUserId;

    @Schema(description = "计划描述")
    @ExcelProperty("计划描述")
    private String desc;

    @Schema(description = "附件地址", example = "https://www.iocoder.cn")
    @ExcelProperty("附件地址")
    private String fileUrl;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "物资 物资-需用计划-单项列表")
    private List<Item> items;
    @Data
    public static class Item extends ProductWithoutIdDO {
        @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21627")
        @ExcelProperty("编号")
        private Long id;

        @Schema(description = "需要计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10329")
        @ExcelProperty("需要计划ID")
        private Long requirementId;

        @Schema(description = "产品类型,从数据字典取:材料、器具", example = "1")
        @ExcelProperty("产品类型,从数据字典取:材料、器具")
        private String productType;

        @Schema(description = "产品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "25678")
        @ExcelProperty("产品编号")
        private Long productId;

        @Schema(description = "产品单位单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "20124")
        @ExcelProperty("产品单位单位")
        private Long productUnitId;

        @Schema(description = "产品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1857")
        @ExcelProperty("产品数量")
        private BigDecimal productCount;

        @Schema(description = "计划首批到场日期", requiredMode = Schema.RequiredMode.REQUIRED)
        @ExcelProperty("计划首批到场日期")
        private LocalDateTime firstBatchArrivalTime;

        @Schema(description = "计划末批到场日期", requiredMode = Schema.RequiredMode.REQUIRED)
        @ExcelProperty("计划末批到场日期")
        private LocalDateTime finalBatchArrivalTime;

        @Schema(description = "用于工程部位")
        @ExcelProperty("用于工程部位")
        private String usedForProjectPart;

        @Schema(description = "描述")
        @ExcelProperty("描述")
        private String desc;

        @Schema(description = "附件地址", example = "https://www.iocoder.cn")
        @ExcelProperty("附件地址")
        private String fileUrl;

        @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
        @ExcelProperty("创建时间")
        private LocalDateTime createTime;

        @Schema(description = "备注", example = "随便")
        @ExcelProperty("备注")
        private String remark;
    }



}