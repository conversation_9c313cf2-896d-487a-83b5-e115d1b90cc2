<template>
  <div class="flex">
    <!-- 左侧树形结构 -->
    <div class="w-30% pr-16px">
      <ContentWrap>
        <div class="mb-16px text-lg font-bold">单位工程</div>
        <el-tree
          :data="treeData"
          :props="treeProps"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="flex items-center">
              <span>{{ node.label }}</span>
              <span class="ml-auto flex gap-1">
                <el-button
                  size="small"
                  type="primary"
                  link
                  @click.stop="handleAddChild(node, data)"
                >
                  新增
                </el-button>
                <el-button size="small" type="warning" link @click.stop="handleEdit(node, data)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" link @click.stop="handleDelete(node, data)">
                  删除
                </el-button>
              </span>
            </span>
          </template>
        </el-tree>
      </ContentWrap>
    </div>

    <!-- 右侧内容 -->
    <div class="w-70%">
      <!-- 表单对话框 -->
      <ProjectStructureForm
        v-model:visible="formVisible"
        :form-type="formType"
        :parent-options="parentOptions"
        :form-data="formData"
        @success="handleFormSuccess"
      />
      <ContentWrap>
        <!-- 搜索工作栏 -->
        <el-form
          ref="queryFormRef"
          :inline="true"
          :model="queryParams"
          class="-mb-15px"
          label-width="85px"
        >
          <el-form-item label="工程名称" prop="name">
            <el-input
              v-model="queryParams.name"
              class="!w-240px"
              clearable
              placeholder="请输入工程名称"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery">
              <Icon class="mr-5px" icon="ep:search" />
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon class="mr-5px" icon="ep:refresh" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <!-- 列表 -->
      <ContentWrap>
        <el-table v-loading="loading" :data="list">
          <el-table-column min-width="150" align="center" label="工程名称" prop="name" />
          <el-table-column min-width="150" align="center" label="工程编码" prop="code" />
          <el-table-column min-width="150" align="center" label="上级工程" prop="parentName" />
          <el-table-column min-width="150" align="center" label="描述" prop="description" />
          <el-table-column min-width="150" align="center" label="操作人" prop="operator" />
          <el-table-column
            :formatter="dateFormatter"
            align="center"
            label="更新时间"
            prop="updateTime"
            width="180"
          />
          <el-table-column min-width="150" align="center" label="操作">
            <template #default="scope">
              <el-button
                size="small"
                type="warning"
                link
                @click="handleEdit(scope.$index, scope.row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                link
                @click="handleDelete(scope.$index, scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          v-model:limit="queryParams.pageSize"
          v-model:page="queryParams.pageNo"
          :total="total"
          @pagination="getList"
        />
      </ContentWrap>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as ProjectApi from '@/api/business/project/structure'
import ProjectStructureForm from './components/ProjectStructureForm.vue'
interface TreeProps {
  label: string
  children?: string
}

interface FormData {
  id?: number
  parentId?: number
  name: string
  code: string
  description: string
}

defineOptions({ name: 'ProjectStructure' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 树形数据
interface TreeNode {
  id: number
  name: string
  children?: TreeNode[]
}

// 树节点点击事件
const handleNodeClick = (data: TreeNode) => {
  queryParams.parentId = data.id
  getList()
}

// 表单相关状态
const formVisible = ref(false)
const formType = ref<'unit' | 'subunit' | 'item'>('unit')
const formData = ref<Partial<FormData>>({
  name: '',
  code: '',
  description: ''
})
const parentOptions = ref<Array<{ id: number; name: string }>>([])

// 新增子节点
const handleAddChild = (node: any, data: TreeNode) => {
  formType.value = data.id ? 'subunit' : 'unit'
  formData.value = { parentId: data.id }
  parentOptions.value = [{ id: data.id, name: data.name }]
  formVisible.value = true
}

// 编辑节点
const handleEdit = (node: any, data: TreeNode) => {
  formType.value = 'unit'
  formData.value = { id: data.id, name: data.name }
  formVisible.value = true
}

// 获取树形数据
const getTreeData = async () => {
  try {
    const { data } = await ProjectApi.getProjectStructureTree()
    treeData.value = data
  } catch (error) {
    console.error('获取树形数据失败:', error)
  }
}

// 删除节点
const handleDelete = async (node: any, data: TreeNode) => {
  try {
    await message.delConfirm()
    await ProjectApi.deleteProjectStructure(data.id)
    message.success('删除成功')
    await getTreeData() // 刷新树形数据
    getList() // 刷新表格数据
  } catch {
    // 用户取消删除
  }
}

const treeData = ref<TreeNode[]>([])
const treeProps: TreeProps = {
  label: 'name',
  children: 'children'
}

const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  code: '',
  parentId: undefined as number | undefined,
  description: '',
  operator: '',
  updateTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询项目结构列表 */
const getList = async () => {
  loading.value = true
  try {
    // 对queryParams 清除空值
    Object.keys(queryParams).forEach((key) => {
      if (queryParams[key] === '' || queryParams[key] === undefined) {
        delete queryParams[key]
      }
    })
    const data = await ProjectApi.getProjectStructurePage(queryParams)
    list.value = data.list
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 表单提交成功回调 */
const handleFormSuccess = () => {
  getTreeData()
  getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
  getTreeData()
})
</script>
