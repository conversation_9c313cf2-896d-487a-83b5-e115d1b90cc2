<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!-- 单位工程表单 -->
      <template v-if="formType === 'unit'">
        <el-form-item label="单位工程名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入单位工程名称" />
        </el-form-item>
        <el-form-item label="工程编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入工程编码" />
        </el-form-item>
        <el-form-item label="工程描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入工程描述"
          />
        </el-form-item>
      </template>

      <!-- 子单位工程表单 -->
      <template v-else-if="formType === 'subunit'">
        <el-form-item label="上级工程" prop="parentId">
          <el-select
            v-model="formData.parentId"
            placeholder="请选择上级工程"
            filterable
          >
            <el-option
              v-for="item in parentOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="子单位工程名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入子单位工程名称" />
        </el-form-item>
        <el-form-item label="工程编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入工程编码" />
        </el-form-item>
        <el-form-item label="工程描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入工程描述"
          />
        </el-form-item>
      </template>

      <!-- 分项工程表单 -->
      <template v-else-if="formType === 'item'">
        <el-form-item label="上级工程" prop="parentId">
          <el-select
            v-model="formData.parentId"
            placeholder="请选择上级工程"
            filterable
          >
            <el-option
              v-for="item in parentOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分项工程名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入分项工程名称" />
        </el-form-item>
        <el-form-item label="工程编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入工程编码" />
        </el-form-item>
        <el-form-item label="工程描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入工程描述"
          />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import * as ProjectApi from '@/api/business/project/structure'

interface FormData {
  id?: number
  parentId?: number
  name: string
  code: string
  description: string
}

interface Props {
  formType: 'unit' | 'subunit' | 'item'
  parentOptions: Array<{ id: number; name: string }>
  visible: boolean
  formData?: Partial<FormData>
}

const props = defineProps<Props>()
const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const dialogTitle = computed(() => {
  switch (props.formType) {
    case 'unit':
      return '新增单位工程'
    case 'subunit':
      return '新增子单位工程'
    case 'item':
      return '新增分项工程'
    default:
      return ''
  }
})

const formData = reactive<FormData>({
  name: '',
  code: '',
  description: '',
  ...props.formData
})

const formRules = reactive<FormRules<FormData>>({
  name: [{ required: true, message: '请输入工程名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入工程编码', trigger: 'blur' }],
  parentId: [{ required: true, message: '请选择上级工程', trigger: 'change' }]
})

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    if (formData.id) {
      await ProjectApi.updateProjectStructure(formData)
    } else {
      await ProjectApi.createProjectStructure(formData)
    }
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单提交失败:', error)
  }
}
</script>
