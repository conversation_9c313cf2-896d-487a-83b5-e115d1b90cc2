import { Layout } from '@/utils/routerHelper'

export default {
    path: '/material-center',
    component: Layout,
    name: 'materialCenter',
    meta: {
      hidden: true
    },
    children: [
      // 合同管理
      {
        path: 'material-center/contract',
        name: 'ContractManagement',
        meta: {
          title: '合同管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/contract'
        },
        component: () => import('@/BusinessModule/MaterialCenter/ContractManagement/ContractManagement.vue')
      },
      {
        path: 'material-center/contract/add',
        name: 'AddContract',
        meta: {
          title: '新增合同',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/contract'
        },
        component: () => import('@/BusinessModule/MaterialCenter/ContractManagement/AddContract.vue')
      },

      // 库存管理
      {
        path: 'material-center/inventory/stock',
        name: 'StockManagement',
        meta: {
          title: '库存管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockManagement/StockManagement.vue')
      },
      {
        path: 'material-center/inventory/inspection',
        name: 'InspectionForm',
        meta: {
          title: '验收单管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/inspection'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/InspectionForm/InspectionForm.vue')
      },
      {
        path: 'material-center/inventory/inspection/add',
        name: 'AddInspectionForm',
        meta: {
          title: '新增验收单',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/inspection'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/InspectionForm/AddInspectionForm.vue')
      },
      {
        path: 'material-center/inventory/return',
        name: 'ReturnForm',
        meta: {
          title: '退货单管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/return'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/ReturnForm/ReturnForm.vue')
      },
      {
        path: 'material-center/inventory/return/add',
        name: 'AddReturnForm',
        meta: {
          title: '新增退货单',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/return'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/ReturnForm/AddReturnForm.vue')
      },
      {
        path: 'material-center/inventory/stock-in',
        name: 'StockIn',
        meta: {
          title: '入库单管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock-in'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockIn/StockIn.vue')
      },
      {
        path: 'material-center/inventory/stock-in/add',
        name: 'AddStockIn',
        meta: {
          title: '新增入库单',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock-in'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockIn/AddStockIn.vue')
      },
      {
        path: 'material-center/inventory/stock-out',
        name: 'StockOut',
        meta: {
          title: '出库单管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock-out'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockOut/StockOut.vue')
      },
      {
        path: 'material-center/inventory/stock-out/add',
        name: 'AddStockOut',
        meta: {
          title: '新增出库单',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock-out'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockOut/AddStockOut.vue')
      },
      {
        path: 'material-center/inventory/stock-back',
        name: 'StockBack',
        meta: {
          title: '退库单管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock-back'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockBack/StockBack.vue')
      },
      {
        path: 'material-center/inventory/stock-back/add',
        name: 'AddStockBack',
        meta: {
          title: '新增退库单',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/inventory/stock-back'
        },
        component: () => import('@/BusinessModule/MaterialCenter/InventoryManagement/StockBack/AddStockBack.vue')
      },

      // 物料资源管理
      {
        path: 'material-center/material-resource/category',
        name: 'MaterialCategoryDict',
        meta: {
          title: '物料分类字典',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/material-resource/category'
        },
        component: () => import('@/BusinessModule/MaterialCenter/MaterialResourceManagement/MaterialCategoryDict.vue')
      },
      {
        path: 'material-center/material-resource/material',
        name: 'MaterialDict',
        meta: {
          title: '物料字典',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/material-resource/material'
        },
        component: () => import('@/BusinessModule/MaterialCenter/MaterialResourceManagement/MaterialDict.vue')
      },

      // 供应商管理
      {
        path: 'material-center/supplier',
        name: 'SupplierManagement',
        meta: {
          title: '供应商管理',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/supplier'
        },
        component: () => import('@/BusinessModule/MaterialCenter/SupplierManagement/SupplierManagement.vue')
      },
      {
        path: 'material-center/supplier/add',
        name: 'AddSupplier',
        meta: {
          title: '新增供应商',
          noCache: true,
          hidden: true,
          activeMenu: 'material-center/supplier'
        },
        component: () => import('@/BusinessModule/MaterialCenter/SupplierManagement/AddSupplier.vue')
      }
    ]
  }
