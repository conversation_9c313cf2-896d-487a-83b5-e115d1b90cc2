declare module '@/api/material/inspection' {
  interface ArrivalInspectionSaveReqVO {
    id?: number
    inspectionCode: string
    inspectionType: string
    inspectionName: string
    inspectionAddr: string
    inspectionTime: string
    inspectionStatus?: string
    projectCode: string
    projectName: string
    supplierId: number
    builderDept: string
    productName: string
    productKey: string
    productNum: number
    productSpecification: string
    appearance?: string
    opinion?: string
    fileUrl?: string
    remark?: string
    fileUrlList?: array<any>
  }

  interface ArrivalInspectionRespVO {
    id?: number
    inspectionCode: string
    inspectionType: string
    inspectionName: string
    inspectionAddr: string
    inspectionTime: string
    inspectionStatus?: string
    projectCode: string
    projectName: string
    supplierId: number
    supplierName: string
    builderDept: string
    productName: string
    productKey: string
    productNum: number
    productSpecification: string
    appearance?: string
    opinion?: string
    fileUrl?: string
    createTime: string
    remark?: string
  }

  export const uploadUrl: string
  export const getInspectionPage: (params: any) => Promise<any>
  export const getInspectionDetail: (id: number) => Promise<any>
  export const createInspection: (data: ArrivalInspectionSaveReqVO) => Promise<any>
  export const updateInspection: (data: ArrivalInspectionSaveReqVO) => Promise<any>
  export const deleteInspection: (id: number) => Promise<any>
  export const exportInspection: (params: any) => Promise<any>
}

declare module '@/api/material/plan-review' {
  interface PlanReviewSaveReqVO {
    id?: number
    planCode: string
    planType: string
    planName: string
    planStatus?: string
    projectCode: string
    projectName: string
    supplierId: number
    supplierName: string
    contentType: string
    contentCode: string
    fileUrl?: string
    remark?: string
    fileUrlList?: array<any>
    contractorOrg?: string
    contractorDept?: string
    architectOrg?: string
    architectDept?: string
    supervisorOrg?: string
    supervisorDept?: string
  }

  interface PlanReviewRespVO {
    id?: number
    planCode: string
    planType: string
    planName: string
    planStatus?: string
    projectCode: string
    projectName: string
    supplierId: number
    supplierName: string
    contentType: string
    contentCode: string
    fileUrl?: string
    createTime: string
    remark?: string
    contractorOrg?: string
    contractorDept?: string
    architectOrg?: string
    architectDept?: string
    supervisorOrg?: string
    supervisorDept?: string
  }

  export const uploadUrl: string
  export const getPlanReviewPage: (params: any) => Promise<any>
  export const getPlanReviewDetail: (id: number) => Promise<any>
  export const createPlanReview: (data: PlanReviewSaveReqVO) => Promise<any>
  export const updatePlanReview: (data: PlanReviewSaveReqVO) => Promise<any>
  export const deletePlanReview: (id: number) => Promise<any>
  export const exportPlanReview: (params: any) => Promise<any>
}

declare module '@/utils/dict' {
  interface DICT_TYPE {
    MATERIAL_PLAN_TYPE: string
    MATERIAL_PLAN_STATUS: string
    MATERIAL_INSPECTION_TYPE: string
    MATERIAL_INSPECTION_STATUS: string
  }

  export function getIntDictOptions(dictType: string): Array<{value: number, label: string}>
  export function getDictOptions(dictType: string): Array<{value: string, label: string}>
  export function getStrDictOptions(dictType: string): Array<{value: string, label: string}>
}
