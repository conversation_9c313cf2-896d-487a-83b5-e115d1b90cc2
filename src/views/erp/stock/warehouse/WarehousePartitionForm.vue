<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
    :disabled="disabled"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label=" " type="index" align="center" width="40" />
      <el-table-column label="分区名称" min-width="120" prop="name">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.name`" :rules="formRules.name" class="mb-0px!">
            <el-input v-model="row.name" placeholder="请输入分区名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150" prop="remark">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60" v-if="!disabled">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3" v-if="!disabled">
    <el-button @click="handleAdd" round>+ 添加分区</el-button>
  </el-row>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import type { WarehousePartitionVO } from '@/api/erp/stock/warehouse'

const props = defineProps<{
  items: WarehousePartitionVO[]
  disabled: boolean
}>()

const formLoading = ref(false)
const formData = ref<WarehousePartitionVO[]>([])
const formRules = reactive({
  name: [{ required: true, message: '分区名称不能为空', trigger: 'blur' }]
})
const formRef = ref<FormInstance>()

/** 初始化数据 */
watch(
  () => props.items,
  (val) => {
    formData.value = val || []
  },
  { immediate: true }
)

/** 添加分区项 */
const handleAdd = () => {
  formData.value.push({
    id: null as unknown as number,
    warehouseId: null as unknown as number,
    name: '',
    remark: ''
  })
}

/** 删除分区项 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单验证 */
const validate = () => {
  return formRef.value?.validate()
}
defineExpose({ validate })

/** 默认添加一个空行 */
onMounted(() => {
  if (formData.value.length === 0 && !props.disabled) {
    handleAdd()
  }
})
</script>
