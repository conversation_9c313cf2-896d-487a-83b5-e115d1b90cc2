import request from '@/config/axios'
import {Supplier<PERSON>pi as supplierApi} from '@/api/erp/purchase/supplier/index'
import {ProductApi} from '@/api/erp/product/product/index'


// 采购订单产品项
export interface PurchaseOrderItem {
  id?: number
  productId?: number
  productUnitName?: string // 产品单位名称（逻辑中使用）
  productBarCode?: string // 产品条码（逻辑中使用）
  stockCount?: number // 库存数量（逻辑中使用）
  count?: number // 采购数量
  boxNumber?: string // 包装箱号
  remark?: string // 备注
}

// 到货验资请求VO
export interface ArrivalInspectionSaveReqVO {
  id?: number
  inspectionCode: string // 检查单编号
  inspectionType: string // 检查单类型
  inspectionName: string // 检查单名称
  inspectionAddr: string // 检查地址
  inspectionTime: string // 检查时间
  inspectionStatus?: string // 检查状态
  projectCode: string // 工程编号
  projectName: string // 工程名称
  supplierId: number // 供应商编号
  builderDept: string // 施工单位
  productName: string // 产品名称
  productKey: string // 产品标识
  productNum: number // 产品数量
  productSpecification: string // 产品规格
  appearance?: string // 外观检查
  opinion?: string // 意见
  fileUrl?: string // 附件地址
  remark?: string // 备注
  items: PurchaseOrderItem[] // 采购产品项列表
}

// 到货验资响应VO
export interface ArrivalInspectionRespVO {
  id?: number
  inspectionCode: string // 检查单编号
  inspectionType: string // 检查单类型
  inspectionName: string // 检查单名称
  inspectionAddr: string // 检查地址
  inspectionTime: string // 检查时间
  inspectionStatus?: string // 检查状态
  projectCode: string // 工程编号
  projectName: string // 工程名称
  supplierId: number // 供应商编号
  supplierName: string // 供应商名称
  builderDept: string // 施工单位
  productName: string // 产品名称
  productKey: string // 产品标识
  productNum: number // 产品数量
  productSpecification: string // 产品规格
  appearance?: string // 外观检查
  opinion?: string // 意见
  fileUrl?: string // 附件地址
  createTime: string // 创建时间
  remark?: string // 备注
  items: PurchaseOrderItem[] // 采购产品项列表
}

// 查询到货验资列表
export const getInspectionPage = async (params) => {
  return await request.get({ url: '/materials/arrival-inspection/page', params })
}

// 删除到货验资
export const deleteInspection = async (id: number) => {
  return await request.delete({ url: `/materials/arrival-inspection/delete?id=${id}` })
}

// 查询到货验资详情
export const getInspectionDetail = async (id: number) => {
  return await request.get({ url: `/materials/arrival-inspection/get?id=${id}` })
}

// 创建到货验资
export const createInspection = async (data: ArrivalInspectionSaveReqVO) => {
  return await request.post({ url: '/materials/arrival-inspection/create', data })
}

// 更新到货验资
export const updateInspection = async (data: ArrivalInspectionSaveReqVO) => {
  return await request.put({ url: '/materials/arrival-inspection/update', data })
}

// 导出到货验资Excel
export const exportInspection = async (params) => {
  return await request.download({ url: '/materials/arrival-inspection/export-excel', params })
}


export const getSupplierSimpleList = supplierApi.getSupplierSimpleList

export const getProductSimpleList = ProductApi.getProductSimpleList
// 
export const uploadUrl = '/materials/arrival-inspection/upload'
