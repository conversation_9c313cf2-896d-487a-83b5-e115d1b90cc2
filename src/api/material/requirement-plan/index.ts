import request from '@/config/axios'

// 物资需用计划单项
export interface RequirementPlanItem {
  /** 编号 */
  id?: number
  /** 产品编号 */
  productId?: number
  /** 产品类型,从数据字典取:材料、器具 */
  productType?: string
  /** 产品单位单位 */
  productUnitId?: number
  /** 产品数量 */
  productCount?: number
  /** 计划首批到场日期 */
  firstBatchArrivalTime?: string
  /** 计划末批到场日期 */
  finalBatchArrivalTime?: string
  /** 用于工程部位 */
  usedForProjectPart?: string
  /** 描述 */
  desc?: string
  /** 附件地址 */
  fileUrl?: string
  /** 备注 */
  remark?: string
}

// 物资需用计划保存请求VO
export interface RequirementPlanSaveReqVO {
  /** 主键ID */
  id?: number
  /** 需用计划编号 */
  no: string
  /** 需用类型 */
  requirementType?: string
  /** 编制单位 */
  establishDeptId?: string
  /** 编制人 */
  establishUserId?: string
  /** 计划描述 */
  desc?: string
  /** 附件地址 */
  fileUrl?: string
  /** 备注 */
  remark?: string
  /** 物资 物资-需用计划-单项列表 */
  items: RequirementPlanItem[]
}

// 物资需用计划响应VO
export interface RequirementPlanRespVO {
  /** 主键ID */
  id?: number
  /** 需用计划编号 */
  no: string
  /** 需用类型 */
  requirementType?: string
  /** 编制单位 */
  establishDeptId?: string
  /** 编制人 */
  establishUserId?: string
  /** 计划描述 */
  desc?: string
  /** 附件地址 */
  fileUrl?: string
  /** 创建时间 */
  createTime: string
  /** 备注 */
  remark?: string
  /** 物资 物资-需用计划-单项列表 */
  items: RequirementPlanItem[]
}

// 项目需求池汇总VO
export interface RequirementPlanSummaryVO {
  /** 产品单位单位名称 */
  productUnitName?: string
  /** 产品规格 */
  productStandard?: string
  /** 产品型号 */
  productModel?: string
  /** 生产厂家 */
  productManufacturer?: string
  /** 产品名称 */
  productName?: string
  /** 产品分类编号 */
  categoryId?: number
  /** 产品分类 */
  categoryName?: string
  /** 产品条码 */
  barCode?: string
  /** 产品单位单位名称 */
  unitName?: string
  /** 产品编号 */
  productId?: number
  /** 产品单位单位 */
  productUnitId?: number
  /** 产品类型,从数据字典取:材料、器具 */
  productType?: string
  /** 产品数量 */
  productCount?: number
}

// 查询物资需用计划分页
export const getRequirementPlanPage = async (params) => {
  return await request.get({ url: '/materials/requirement-plan/page', params })
}

// 删除物资需用计划
export const deleteRequirementPlan = async (id: number) => {
  return await request.delete({ url: `/materials/requirement-plan/delete?id=${id}` })
}

// 查询物资需用计划详情
export const getRequirementPlanDetail = async (id: number) => {
  return await request.get({ url: `/materials/requirement-plan/get?id=${id}` })
}

// 创建物资需用计划
export const createRequirementPlan = async (data: RequirementPlanSaveReqVO) => {
  return await request.post({ url: '/materials/requirement-plan/create', data })
}

// 更新物资需用计划
export const updateRequirementPlan = async (data: RequirementPlanSaveReqVO) => {
  return await request.put({ url: '/materials/requirement-plan/update', data })
}

// 导出物资需用计划Excel
export const exportRequirementPlan = async (params) => {
  return await request.download({ url: '/materials/requirement-plan/export-excel', params })
}

// 获取项目需求池汇总
export const getRequirementPlanSummary = async (params: obnject) => {
  return await request.get({ url: '/materials/requirement-plan/summary', params })
}
