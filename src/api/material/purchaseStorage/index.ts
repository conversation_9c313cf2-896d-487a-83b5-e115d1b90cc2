import request from '@/config/axios'

// 采购订单项
export interface Item {
  /** 订单项编号 */
  id?: number
  /** 产品编号 */
  productId?: number
  /** 产品单位ID */
  productUnitId?: number
  /** 产品单价 */
  productPrice?: number
  /** 产品数量 */
  count?: number
  /** 备注 */
  remark?: string
  /** 采购入库数量 */
  inCount?: number
  /** 采购退货数量 */
  returnCount?: number
  /** 库存数量 */
  stockCount?: number
  /** 包装箱号 */
  packageNo?: string
  /** 采购订单项ID */
  purchaseOrderItemId?: number
  /** 物资需要计划项ID */
  requirementItemId?: number
  /** 物资分类: 材料 器械 */
  productType?: string
  /** 采购数量 */
  purchaseCount?: number
}

// 采购订单请求VO
export interface AogOrderSaveReqVO {
  /** 订单ID */
  id?: number
  /** 供应商ID */
  supplierId?: number
  /** 订单时间 */
  orderTime?: string
  /** 附件URL */
  fileUrl?: string
  /** 备注 */
  remark?: string
  /** 检查地址 */
  inspectionAddr?: string
  /** 外观检查结果 */
  appearance?: string
  /** 送货单号 */
  deliveryNo?: string
  /** 配送方式 */
  deliveryType?: string
  /** 采购单号 */
  purchaseOrderNo?: string
  /** 接收类型 */
  receiveType?: string
  /** 接收人ID */
  receiveUserId?: string
  /** 质检结果 */
  qcResult?: string
  /** 质检不合格原因 */
  qcUnqualifiedReason?: string
  /** 质检处理意见 */
  qcOpinion?: string
  /** 验收结果 */
  inspectionResult?: string
  /** 验收意见 */
  inspectionOpinion?: string
  /** 仓库ID */
  warehouseId?: number
  /** 仓库分区ID */
  warehousePartitionId?: number
  /** 质检员签名 */
  signQc?: string
  /** 仓管员签名 */
  signWarehouse?: string
  /** 项目负责人签名 */
  signProjectLeader?: string
  /** 入库操作员 */
  operatorIn?: string
  /** 退货操作员 */
  operatorReturn?: string
  /** 物资部签名 */
  signMaterial?: string
  /** 入库类型 */
  inType?: string
  /** 到货单据URL */
  deliveryFileUrl?: string
  /** 物资需求计划ID */
  requirementId?: number
  /** 物资需求计划编号 */
  requirementNo?: string
  /** 采购订单ID */
  purchaseOrderId?: number
  /** 参检人员 */
  inspectionUserId?: string
  /** 参检部门 */
  inspectionDeptId?: string
  /** 入库操作类型：全部入库/让步入库 */
  inCategory?: string
  /** 订单项列表 */
  items?: Item[]
}

// 采购订单响应VO
export interface AogOrderRespVO {
  /** 订单ID */
  id?: number
  /** 采购单编号 */
  no?: string
  /** 采购状态 */
  status?: number
  /** 供应商ID */
  supplierId?: number
  /** 供应商名称 */
  supplierName?: string
  /** 采购时间 */
  orderTime?: string
  /** 合计数量 */
  totalCount?: number
  /** 合计产品价格(元) */
  totalProductPrice?: number
  /** 附件URL */
  fileUrl?: string
  /** 备注 */
  remark?: string
  /** 检查地址 */
  inspectionAddr?: string
  /** 外观检查结果 */
  appearance?: string
  /** 创建人 */
  creator?: string
  /** 创建人名称 */
  creatorName?: string
  /** 创建时间 */
  createTime?: string
  /** 订单项列表 */
  items?: Item[]
  /** 产品信息 */
  productNames?: string
  /** 采购入库数量 */
  inCount?: number
  /** 采购退货数量 */
  returnCount?: number
  /** 送货单号 */
  deliveryNo?: string
  /** 配送方式 */
  deliveryType?: string
  /** 采购单号 */
  purchaseOrderNo?: string
  /** 接收类型 */
  receiveType?: string
  /** 接收人ID */
  receiveUserId?: string
  /** 质检结果 */
  qcResult?: string
  /** 质检不合格原因 */
  qcUnqualifiedReason?: string
  /** 质检处理意见 */
  qcOpinion?: string
  /** 验收结果 */
  inspectionResult?: string
  /** 验收意见 */
  inspectionOpinion?: string
  /** 仓库ID */
  warehouseId?: number
  /** 仓库分区ID */
  warehousePartitionId?: number
  /** 质检员签名 */
  signQc?: string
  /** 仓管员签名 */
  signWarehouse?: string
  /** 项目负责人签名 */
  signProjectLeader?: string
  /** 入库操作员 */
  operatorIn?: string
  /** 退货操作员 */
  operatorReturn?: string
  /** 物资部签名 */
  signMaterial?: string
  /** 入库类型 */
  inType?: string
  /** 到货单据URL */
  deliveryFileUrl?: string
  /** 物资需求计划ID */
  requirementId?: number
  /** 物资需求计划编号 */
  requirementNo?: string
  /** 采购订单ID */
  purchaseOrderId?: number
  /** 参检人员 */
  inspectionUserId?: string
  /** 参检部门 */
  inspectionDeptId?: string
  /** 入库操作类型：全部入库/让步入库 */
  inCategory?: string
}

// 查询采购订单分页
export const getAogOrderPage = async (params) => {
  return await request.get({ url: '/materials/aog-order/page', params })
}

// 删除采购订单
export const deleteAogOrder = async (ids: number[]) => {
  return await request.delete({ url: '/materials/aog-order/delete', params: { ids } })
}

// 查询采购订单详情
export const getAogOrderDetail = async (id: number) => {
  return await request.get({ url: '/materials/aog-order/get', params: { id } })
}

// 创建采购订单
export const createAogOrder = async (data: AogOrderSaveReqVO) => {
  return await request.post({ url: '/materials/aog-order/create', data })
}

// 更新采购订单
export const updateAogOrder = async (data: AogOrderSaveReqVO) => {
  return await request.put({ url: '/materials/aog-order/update', data })
}

// 更新采购订单状态
export const updateAogOrderStatus = async (id: number, status: number) => {
  return await request.put({
    url: '/materials/aog-order/update-status',
    params: { id, status }
  })
}

// 导出采购订单Excel
export const exportAogOrder = async (params) => {
  return await request.download({ url: '/materials/aog-order/export-excel', params })
}

// 采购订单入库
export const orderIn = async (data: AogOrderSaveReqVO) => {
  return await request.post({ url: '/materials/aog-order/in', data })
}

// 采购订单退货
export const orderOut = async (data: AogOrderSaveReqVO) => {
  return await request.post({ url: '/materials/aog-order/out', data })
}
