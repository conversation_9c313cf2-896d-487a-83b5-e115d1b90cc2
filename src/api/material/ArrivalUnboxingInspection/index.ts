import request from '@/config/axios'
import {Supplier<PERSON>pi as supplierApi} from '@/api/erp/purchase/supplier/index'
import {ProductApi} from '@/api/erp/product/product/index'
// 进场开箱验收单项
export interface Item {
  id?: number
  inspectionId?: string
  productId: number
  productUnitId: number
  productCount: number
  packageNo?: string
  remark?: string
  needApproachCheck: number
}

// 进场开箱验收单请求VO
export interface ApproachInspectionSaveReqVO {
  id?: number
  inspectionCode: string
  inspectionType: string
  inspectionName: string
  inspectionAddr: string
  inspectionTime: string
  inspectionStatus?: string
  projectCode: string
  projectName: string
  supplierId: number
  contractNo: string
  contractName: string
  builderDept: string
  productNames?: string
  fileUrl?: string
  remark?: string
  processInstanceId?: string
  processStatus?: number
  items: Item[]
}

// 进场开箱验收单响应VO
export interface ApproachInspectionRespVO {
  id?: number
  inspectionCode: string
  inspectionType: string
  inspectionName: string
  inspectionAddr: string
  inspectionTime: string
  inspectionStatus?: string
  projectCode: string
  projectName: string
  supplierId: number
  contractNo: string
  contractName: string
  builderDept: string
  productNames?: string
  fileUrl?: string
  createTime?: string
  remark?: string
  processInstanceId?: string
  processStatus?: number
  items: Item[]
}

// 创建进场开箱验收单
export const createApproachInspection = async (data: ApproachInspectionSaveReqVO) => {
  return await request.post({ url: '/materials/approach-inspection/create', data })
}

// 删除进场开箱验收单
export const deleteApproachInspection = async (id: number) => {
  return await request.delete({ url: `/materials/approach-inspection/delete?id=${id}` })
}

// 导出进场开箱验收单Excel
export const exportApproachInspection = async (params) => {
  return await request.download({ url: '/materials/approach-inspection/export-excel', params })
}

// 获取进场开箱验收单详情
export const getApproachInspection = async (id: number) => {
  return await request.get({ url: `/materials/approach-inspection/get?id=${id}` })
}

// 获取进场开箱验收单分页
export const getApproachInspectionPage = async (params) => {
  return await request.get({ url: '/materials/approach-inspection/page', params })
}

// 更新进场开箱验收单
export const updateApproachInspection = async (data: ApproachInspectionSaveReqVO) => {
  return await request.put({ url: '/materials/approach-inspection/update', data })
}

// 获取供应商简单列表
export const getSupplierSimpleList = supplierApi.getSupplierSimpleList

// 获取产品简单列表
export const getProductSimpleList = ProductApi.getProductSimpleList
