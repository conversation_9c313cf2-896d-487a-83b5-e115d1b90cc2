import request from '@/config/axios'
// 计划报审请求VO
export interface PlanReviewSaveReqVO {
  id?: number
  planCode: string // 报表编号
  planType: string // 报表类型
  planName: string // 报表名称
  planStatus?: string // 报表状态
  projectCode: string // 工程编号
  projectName: string // 工程名称
  supplierId: number // 供应商编号
  supplierName: string // 供应商名称
  contentType: string // 内容类型
  contentCode: string // 内容编号
  fileUrl?: string // 附件地址
  fileUrlList?: string[] // 附件地址列表
  remark?: string // 备注
  contractorOrg?: string // 承包单位
  contractorDept?: string // 承包单位部门
  architectOrg?: string // 建设单位
  architectDept?: string // 建设单位部门
  supervisorOrg?: string // 项目监理机构
  supervisorDept?: string // 项目监理机构部门
}

// 计划报审响应VO
export interface PlanReviewRespVO {
  id?: number
  planCode: string // 报表编号
  planType: string // 报表类型
  planName: string // 报表名称
  planStatus?: string // 报表状态
  projectCode: string // 工程编号
  projectName: string // 工程名称
  supplierId: number // 供应商编号
  supplierName: string // 供应商名称
  contentType: string // 内容类型
  contentCode: string // 内容编号
  fileUrl?: string // 附件地址
  fileUrlList?: string[] // 附件地址列表
  createTime: string // 创建时间
  remark?: string // 备注
  contractorOrg?: string // 承包单位
  contractorDept?: string // 承包单位部门
  architectOrg?: string // 建设单位
  architectDept?: string // 建设单位部门
  supervisorOrg?: string // 项目监理机构
  supervisorDept?: string // 项目监理机构部门
}

// 查询计划报审分页
export const getPlanReviewPage = async (params: {
  pageNo: number
  pageSize: number
  planCode?: string
  planType?: string
  planName?: string
  planStatus?: string
  projectCode?: string
  projectName?: string
  supplierId?: number
  supplierName?: string
  contentType?: string
  contentCode?: string
  fileUrl?: string
  createTime?: string[]
  remark?: string
}) => {
  return await request.get({ url: '/materials/plan-review/page', params })
}

// 删除计划报审
export const deletePlanReview = async (id: number) => {
  return await request.delete({ url: `/materials/plan-review/delete?id=${id}` })
}

// 查询计划报审详情
export const getPlanReviewDetail = async (id: number) => {
  return await request.get({ url: `/materials/plan-review/get?id=${id}` })
}

// 创建计划报审
export const createPlanReview = async (data: PlanReviewSaveReqVO) => {
  return await request.post({ url: '/materials/plan-review/create', data })
}

// 更新计划报审
export const updatePlanReview = async (data: PlanReviewSaveReqVO) => {
  return await request.put({ url: '/materials/plan-review/update', data })
}

// 导出计划报审Excel
export const exportPlanReview = async (params) => {
  return await request.download({ url: '/materials/plan-review/export-excel', params })
}

// 上传地址
export const uploadUrl = '/materials/plan-review/upload'
