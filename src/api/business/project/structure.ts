import request from '@/config/axios'

// 查询项目结构分页
export const getProjectStructurePage = (params?: any) => {
  return request.get({
    url: '/business/project/structure/page',
    params
  })
}

// 获取项目结构详情
export const getProjectStructureDetail = (id: number) => {
  return request.get({
    url: `/business/project/structure/get/${id}`
  })
}

// 新增项目结构
export const createProjectStructure = (data: any) => {
  return request.post({
    url: '/business/project/structure/create',
    data
  })
}

// 修改项目结构
export const updateProjectStructure = (data: any) => {
  return request.put({
    url: '/business/project/structure/update',
    data
  })
}

// 删除项目结构
export const deleteProjectStructure = (id: number) => {
  return request.delete({
    url: `/business/project/structure/delete/${id}`
  })
}

// 获取项目结构树
export const getProjectStructureTree = () => {
  return request.get({
    url: '/business/project/structure/tree'
  })
}

// 导出项目结构
export const exportProjectStructure = (params: any) => {
  return request.download({
    url: '/business/project/structure/export',
    params
  })
}
