import request from '@/config/axios'

// ERP 其它出库单项 VO
export interface StockOutItemVO {
  id: number // 分区编号
  warehouseId: number // 仓库编号
  name: string // 分区名称
  remark: string // 备注
}

// ERP 其它出库单 VO
export interface StockOutVO {
  id: number // 出库编号
  no: string // 出库单号
  customerId: number // 客户编号
  customerName: string // 客户名称
  outTime: string // 出库时间
  totalCount: number // 合计数量
  totalPrice: number // 合计金额，单位：元
  status: number // 状态
  remark: string // 备注
  fileUrl: string // 附件 URL
  creator: string // 创建人
  creatorName: string // 创建人名称
  createTime: string // 创建时间
  items: StockOutItemVO[] // 出库项列表
  productNames: string // 产品信息
  receivingUnitId: string // 领料单位
  receiver: string // 领料人
  usedForProjectPart: string // 用于工程部位
  outType: string // 出库单类型
}

// ERP 其它出库单分页查询参数 VO
export interface StockOutPageReqVO {
  pageNo: number
  pageSize: number
  no?: string
  customerId?: number
  outTime?: string[]
  status?: number
  remark?: string
  creator?: string
  productId?: number
  warehouseId?: number
  warehousePartitionId?: number
  receivingUnitId?: string
  receiver?: string
}

// ERP 其它出库单 API
export const StockOutApi = {
  // 查询其它出库单分页
  getStockOutPage: async (params: StockOutPageReqVO) => {
    return await request.get({ url: `/materials/stock-out/page`, params })
  },

  // 查询其它出库单详情
  getStockOut: async (id: number) => {
    return await request.get({ url: `/materials/stock-out/get`, params: { id } })
  },

  // 新增其它出库单
  createStockOut: async (data: StockOutVO) => {
    return await request.post({ url: `/materials/stock-out/create`, data })
  },

  // 修改其它出库单
  updateStockOut: async (data: StockOutVO) => {
    return await request.put({ url: `/materials/stock-out/update`, data })
  },

  // 更新其它出库单的状态
  updateStockOutStatus: async (id: number, status: number) => {
    return await request.put({
      url: `/materials/stock-out/update-status`,
      params: {
        id,
        status
      }
    })
  },

  // 删除其它出库单
  deleteStockOut: async (ids: number[]) => {
    return await request.delete({
      url: `/materials/stock-out/delete`,
      params: {
        ids: ids.join(',')
      }
    })
  },

  // 导出其它出库单 Excel
  exportStockOut: async (params) => {
    return await request.download({ url: `/materials/stock-out/export-excel`, params })
  }
}
