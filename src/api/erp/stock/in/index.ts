import request from '@/config/axios'

// ERP 其它入库单项 VO
export interface Item {
  id: number // 入库项编号
  warehouseId: number // 仓库编号
  warehousePartitionId: number // 仓库分区编号
  productId: number // 产品编号
  productPrice: number // 产品单价
  count: number // 产品数量
  remark?: string // 备注
  outItemId?: number // 领料单项id
  packageNo?: string // 包装箱号
}

// ERP 其它入库单保存 Request VO
export interface StockInSaveReqVO {
  id?: number // 入库编号
  supplierId?: number // 供应商编号
  inTime: string // 入库时间
  remark?: string // 备注
  fileUrl?: string // 附件 URL
  returnDeptId?: string // 归还单位
  returnPerson?: string // 归还人
  usedForProjectPart?: string // 用于工程部位
  outId?: number // 领料单Id
  outNo?: number // 领料单no
  items: Item[] // 入库项列表
  inType?: string // 入库类型
  inspectionResult?: string // 验收结果
  inspectionOpinion?: string // 验收意见
  inspectionUserId?: string // 参检人员
  inspectionDeptId?: string // 参检部门
  signQc?: string // 质检员签名
  signWarehouse?: string // 仓管员签名
  signProjectLeader?: string // 项目负责人签名
  signMaterial?: string // 物资部签名
}

// ERP 其它入库单 Response VO
export interface StockInRespVO {
  id: number // 入库编号
  no: string // 入库单号
  supplierId: number // 供应商编号
  supplierName: string // 供应商名称
  inTime: string // 入库时间
  totalCount: number // 合计数量
  totalPrice: number // 合计金额，单位：元
  status: number // 状态
  remark?: string // 备注
  returnDeptId?: string // 归还单位
  returnPerson?: string // 归还人
  usedForProjectPart?: string // 用于工程部位
  outId?: number // 领料单Id
  outNo?: number // 领料单no
  fileUrl?: string // 附件 URL
  creator?: string // 创建人
  creatorName?: string // 创建人名称
  createTime?: string // 创建时间
  items: Item[] // 入库项列表
  productNames?: string // 产品信息
  inType?: string // 入库类型
  inspectionResult?: string // 验收结果
  inspectionOpinion?: string // 验收意见
  inspectionUserId?: string // 参检人员
  inspectionDeptId?: string // 参检部门
  signQc?: string // 质检员签名
  signWarehouse?: string // 仓管员签名
  signProjectLeader?: string // 项目负责人签名
  signMaterial?: string // 物资部签名
}

// ERP 其它入库单 API
// ERP 其它入库单分页 Request VO
export interface StockInPageReqVO {
  no?: string // 入库单号
  supplierId?: number // 供应商编号
  inTime?: string[] // 入库时间范围
  status?: number // 状态
  remark?: string // 备注
  creator?: string // 创建者
  productId?: number // 产品编号
  warehouseId?: number // 仓库编号
  warehousePartitionId?: number // 仓库分区编号
  returnDeptId?: string // 归还单位
  returnPerson?: string // 归还人
}

export const StockInApi = {
  // 查询其它入库单分页
  getStockInPage: async (params: StockInPageReqVO) => {
    return await request.get({ url: `/materials/stock-in/page`, params })
  },

  // 查询其它入库单详情
  getStockIn: async (id: number) => {
    return await request.get({ url: `/materials/stock-in/get?id=` + id })
  },

  // 新增其它入库单
  createStockIn: async (data: StockInSaveReqVO) => {
    return await request.post({ url: `/materials/stock-in/create`, data })
  },

  // 修改其它入库单
  updateStockIn: async (data: StockInSaveReqVO) => {
    return await request.put({ url: `/materials/stock-in/update`, data })
  },

  // 更新其它入库单的状态
  updateStockInStatus: async (id: number, status: number) => {
    return await request.put({
      url: `/materials/stock-in/update-status`,
      params: {
        id,
        status
      }
    })
  },

  // 删除其它入库单
  deleteStockIn: async (ids: number[]) => {
    return await request.delete({
      url: `/materials/stock-in/delete`,
      params: {
        ids: ids.join(',')
      }
    })
  },

  // 导出其它入库单 Excel
  exportStockIn: async (params) => {
    return await request.download({ url: `/materials/stock-in/export-excel`, params })
  }
}
