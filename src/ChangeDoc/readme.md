# 产品新增/修改
```
添加字段 材料类别
materialType
涉及接口
/erp/product-category/create
/erp/product-category/update
```
# 产品新增
```
model - 型号 (字符串类型)
brand - 品牌 (字符串类型)
material - 材质 (字符串类型)

涉及接口
/erp/product/create
/erp/product/update
```

# 库存信息 
```
货架信息
shelfCount: 货架数量 (数字类型)
shelfCapacity: 货架容量 (数字类型，单位：KG)
partitionInfo: 分区 (下拉选择)

涉及接口
/erp/warehouse/create
/erp/warehouse/update
```


# 供应商
```
统一信用代码
unifiedCreditCode
接口
/erp/supplier/create
/erp/supplier/update
```

# 采购订单
```
合同编号 contractNo
送货人 deliveryUserId
验收信息  acceptanceInfo

/erp/purchase-order/create
/erp/purchase-order/update
```

# 采购入库
```
 供应类型 supplyType

   // 新增采购入库
  createPurchaseIn: async (data: PurchaseInVO) => {
    return await request.post({ url: `/erp/purchase-in/create`, data })
  },

  // 修改采购入库
  updatePurchaseIn: async (data: PurchaseInVO) => {
    return await request.put({ url: `/erp/purchase-in/update`, data })
  },
```

# 其他出库
```
receivingUnitId 领料单位
领料人 receiver

  // 新增其它出库单
  createStockOut: async (data: StockOutVO) => {
    return await request.post({ url: `/erp/stock-out/create`, data })
  },

  // 修改其它出库单
  updateStockOut: async (data: StockOutVO) => {
    return await request.put({ url: `/erp/stock-out/update`, data })
  },

```

# 采购退货
```
contractNo  合同编号
returnUserId  退货人

  // 新增采购退货
  createPurchaseReturn: async (data: PurchaseReturnVO) => {
    return await request.post({ url: `/erp/purchase-return/create`, data })
  },

  // 修改采购退货
  updatePurchaseReturn: async (data: PurchaseReturnVO) => {
    return await request.put({ url: `/erp/purchase-return/update`, data })
  },

```


# 退库的没找到 
