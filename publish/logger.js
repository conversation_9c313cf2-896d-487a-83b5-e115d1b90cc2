const fs = require('fs')

class Logger {
  constructor() {
    this.logStream = fs.createWriteStream('deploy.log', { flags: 'a' })
    this.logLevels = {
      INFO: 1,
      WARN: 2,
      ERROR: 3
    }
    this.currentLevel = this.logLevels.INFO
  }

  log(level, module, message) {
    const timestamp = new Date().toISOString()
    const levelStr = Object.keys(this.logLevels).find(key => this.logLevels[key] === level)
    const logLine = `[${timestamp}] [${levelStr}] [${module}] ${message}`

    // 输出到控制台
    if (level >= this.currentLevel) {
      if (level === this.logLevels.ERROR) {
        console.error(logLine)
      } else {
        console.log(logLine)
      }
    }

    // 写入日志文件
    this.logStream.write(logLine + '\n')
  }

  info(module, message) {
    this.log(this.logLevels.INFO, module, message)
  }

  warn(module, message) {
    this.log(this.logLevels.WARN, module, message)
  }

  error(module, message, error = null) {
    let fullMessage = message
    if (error) {
      fullMessage += `\n${error.stack || error.message}`
    }
    this.log(this.logLevels.ERROR, module, fullMessage)
  }
}

module.exports = Logger
