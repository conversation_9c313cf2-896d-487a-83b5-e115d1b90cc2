const fs = require('fs');
const path = require('path');
const { Client } = require('ssh2');
const scp2 = require('scp2');
const zipFolder = require('./zipFolder');
const util = require('util');

class PublishTool {
  constructor(config) {
    this.config = {
      sourcePath: '../dist-prod',
      remotePath: '/var/www/html',
      zipName: 'release.zip',
      ...config
    };
    this.logger = config.logger || console;
    this.conn = null;
  }

  async zipSource() {
    const { sourcePath, zipName } = this.config;
    this.logger.info('ZIP', `开始打包文件夹: ${sourcePath}`);
    
    try {
      await zipFolder(path.resolve(__dirname, sourcePath), zipName);
      this.logger.info('ZIP', `打包完成: ${zipName}`);
      return path.resolve(__dirname, zipName);
    } catch (err) {
      this.logger.error('ZIP', '打包失败', err);
      throw err;
    }
  }

  async connectToServer() {
    const { server } = this.config;
    this.logger.info('SSH', `正在连接服务器: ${server.host}`);
    
    return new Promise((resolve, reject) => {
      this.conn = new Client();
      
      this.conn.connect({
        host: server.host,
        port: server.port || 22,
        username: server.username,
        privateKey: server.privateKeyPath ? fs.readFileSync(server.privateKeyPath) : undefined,
        password: server.password
      });
      
      this.conn.on('ready', () => {
        this.logger.info('SSH', '服务器连接成功');
        resolve();
      });
      
      this.conn.on('error', (err) => {
        this.logger.error('SSH', '连接失败', err);
        reject(err);
      });
    });
  }

  async backupRemoteDirectory() {
    const { remotePath } = this.config;
    this.logger.info('BACKUP', `检查部署目录是否存在: ${remotePath}`);
    
    return new Promise((resolve, reject) => {
      this.conn.exec(`if [ -d "${remotePath}" ]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        backupDir="${remotePath}_$timestamp"
        mv "${remotePath}" "$backupDir"
        echo "已备份目录: ${remotePath} -> $backupDir"
      else
        echo "目录不存在，无需备份: ${remotePath}"
      fi`, (err, stream) => {
        if (err) return reject(err);
        
        let output = '';
        stream
          .on('data', (data) => output += data)
          .on('close', (code) => {
            if (code !== 0) return reject(new Error(`备份失败: ${output}`));
            
            if (output.includes('已备份目录')) {
              const backupDir = output.match(/已备份目录: .+ -> (.+)/)[1];
              this.logger.info('BACKUP', `目录已备份至: ${backupDir}`);
            } else {
              this.logger.info('BACKUP', output.trim());
            }
            resolve();
          });
      });
    });
  }

  async uploadZip(localZipPath) {
    const { remotePath, zipName } = this.config;
    const remoteZipPath = path.join(remotePath, zipName);
    
    if (!fs.existsSync(localZipPath)) {
      throw new Error(`本地压缩文件不存在: ${localZipPath}`);
    }
    
    const fileStats = fs.statSync(localZipPath);
    const totalSize = fileStats.size;
    let uploadedBytes = 0;
    let lastUpdate = Date.now();
    
    this.logger.info('UPLOAD', `开始上传: ${localZipPath} -> ${remoteZipPath} (${(totalSize / 1024 / 1024).toFixed(2)}MB)`);
    
    return new Promise((resolve, reject) => {
      this.conn.sftp((sftpErr, sftp) => {
        if (sftpErr) return reject(sftpErr);
        
        const writeStream = sftp.createWriteStream(remoteZipPath);
        const fileStream = fs.createReadStream(localZipPath);
        
        writeStream.on('close', () => {
          const uploadTime = (Date.now() - lastUpdate) / 1000;
          const speed = (totalSize / 1024) / uploadTime;
          this.logger.info('UPLOAD', `上传完成 (${(totalSize / 1024 / 1024).toFixed(2)}MB, ${speed.toFixed(2)}KB/s)`);
          resolve();
        });
        
        writeStream.on('error', reject);
        
        fileStream.on('data', (chunk) => {
          uploadedBytes += chunk.length;
          const now = Date.now();
          const elapsed = (now - lastUpdate) / 1000;
          
          if (elapsed > 0.5) {
            const speed = (chunk.length / 1024) / elapsed;
            const percent = ((uploadedBytes / totalSize) * 100).toFixed(1);
            process.stdout.write(`\r上传进度: ${percent}% | ${(uploadedBytes/1024/1024).toFixed(2)}MB/${(totalSize/1024/1024).toFixed(2)}MB | ${speed.toFixed(2)}KB/s`);
            lastUpdate = now;
          }
        });
        
        fileStream.pipe(writeStream);
      });
    });
  }

  async unzipRemoteFile() {
    const { remotePath, zipName } = this.config;
    const remoteZipPath = path.join(remotePath, zipName);
    
    this.logger.info('UNZIP', '开始在服务器解压文件...');
    
    return new Promise((resolve, reject) => {
      this.conn.exec(
        `mkdir -p ${remotePath} && unzip -o \"${remoteZipPath}\" -d \"${remotePath}\" && rm \"${remoteZipPath}\"`,
        (err, stream) => {
          if (err) return reject(err);
          
          let stdout = '';
          let stderr = '';
          
          stream
            .on('data', (data) => stdout += data)
            .stderr.on('data', (data) => stderr += data);
          
          stream.on('close', (code) => {
            if (code !== 0) return reject(new Error(stderr));
            this.logger.info('UNZIP', '解压完成');
            resolve();
          });
        }
      );
    });
  }

  async deploy() {
    try {
      const localZipPath = await this.zipSource();
      await this.connectToServer();
      await this.backupRemoteDirectory();
      await this.uploadZip(localZipPath);
      await this.unzipRemoteFile();
      this.logger.info('DEPLOY', '发布流程全部完成!');
    } catch (error) {
      this.logger.error('DEPLOY', '发布流程失败', error);
      throw error;
    } finally {
      if (this.conn) this.conn.end();
      if (localZipPath) fs.unlinkSync(localZipPath);
    }
  }
}

module.exports = PublishTool;
