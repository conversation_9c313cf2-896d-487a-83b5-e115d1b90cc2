#!/usr/bin/env node
const { exec } = require('child_process')
const readline = require('readline')
const scp2 = require('scp2')
const { Client } = require('ssh2')
const zipFolder = require('./zipFolder')
const fs = require('fs')
const path = require('path')
const util = require('util')
const { cleanAndBuild } = require('./buildCommands')
const Logger = require('./logger')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

const logger = new Logger()

// 获取用户输入
function ask(question, hidden = false) {
  return new Promise((resolve) => {
    if (hidden) {
      const stdin = process.openStdin()
      process.stdin.on('data', (char) => {
        char = char + ''
        switch (char) {
          case '\n':
          case '\r':
          case '\u0004':
            stdin.pause()
            break
          default:
            process.stdout.write(
              '\x1B[2K\x1B[200D' + question + Array(rl.line.length + 1).join('*')
            )
            break
        }
      })
    }

    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

async function main() {
  try {
    logger.info('MAIN', '=== 文件发布脚本启动 ===')

    // 固定参数
    const sourcePath = '../dist-prod' // 与vite.config.ts中的outDir保持一致
    const remotePath = '/var/www/html'
    const uploadPath = '/var/www/html'
    const zipName = 'release.zip'

    // 强制刷新构建目录
    logger.info('BUILD', '开始强制刷新构建目录...')
    try {
      // 删除旧构建目录确保全新构建
      await fs.promises.rm(path.resolve(__dirname, sourcePath), { recursive: true, force: true })
      logger.info('BUILD', '旧构建目录已删除')
      
      // 执行全新构建
      await cleanAndBuild(sourcePath, logger)
    } catch (err) {
      throw err
    }

    // 打包阶段
    logger.info('ZIP', '开始打包文件夹...')
    try {
      await zipFolder(path.resolve(__dirname, sourcePath), zipName)
      logger.info('ZIP', `打包完成: ${zipName}`)
    } catch (err) {
      logger.error('ZIP', '打包失败', err)
      throw err
    }

    // 检查构建目录
    try {
      await fs.promises.access(path.resolve(__dirname, sourcePath))
      logger.info('VERIFY', `构建目录验证通过: ${sourcePath}`)
    } catch (err) {
      logger.error('VERIFY', `构建目录不存在: ${sourcePath}`, err)
      throw err
    }

    // 获取部署参数
    const serverIp = await ask('请输入目标服务器IP地址: ')
    const password = await ask('请输入root密码: ', true)
    const username = process.env.DEPLOY_USER || 'root'
    logger.info('AUTH', `使用账号: ${username}@${serverIp}`)

    // 建立SSH连接
    const conn = new Client()
    logger.info('SSH', '正在连接服务器...')
    
    await new Promise((resolve, reject) => {
      conn.connect({
        host: serverIp,
        port: 22,
        username: username,
        privateKey: process.env.DEPLOY_KEY ? fs.readFileSync(process.env.DEPLOY_KEY) : undefined,
        password: password || undefined
      })
      
      conn.on('ready', () => {
        logger.info('SSH', '服务器连接成功')
        resolve()
      })
      
      conn.on('error', (err) => {
        logger.error('SSH', '连接失败', err)
        reject(err)
      })
    })

    // 备份阶段 - 检查并重命名现有部署目录
    logger.info('BACKUP', `检查部署目录是否存在: ${remotePath}`)
    
    await new Promise((resolve, reject) => {
      conn.exec(`if [ -d "${remotePath}" ]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        backupDir="$(dirname ${remotePath})/backup_$(basename ${remotePath})_$timestamp"
        mv "${remotePath}" "$backupDir"
        echo "已备份目录: ${remotePath} -> $backupDir"
      else
        echo "目录不存在，无需备份: ${remotePath}"
      fi`, (err, stream) => {
        if (err) {
          logger.error('BACKUP', '备份检查失败', err)
          return reject(err)
        }

        let output = ''
        stream
          .on('data', (data) => {
            output += data
          })
          .on('close', (code) => {
            if (code !== 0) {
              logger.error('BACKUP', `备份失败 (code: ${code})`)
              return reject(new Error(`备份失败: ${output}`))
            }
            
            // 记录备份结果
            if (output.includes('已备份目录')) {
              const backupDir = output.match(/已备份目录: .+ -> (.+)/)[1]
              logger.info('BACKUP', `目录已备份至: ${backupDir}`)
            } else {
              logger.info('BACKUP', output.trim())
            }
            resolve()
          })
      })
    })

    // 文件传输
    const remoteZipPath = path.join(remotePath, zipName)
    const localZipPath = path.resolve(__dirname, zipName)
    
    // 验证本地文件是否存在
    if (!fs.existsSync(localZipPath)) {
      throw new Error(`本地压缩文件不存在: ${localZipPath}`)
    }
    
    const fileStats = fs.statSync(localZipPath)
    const totalSize = fileStats.size
    let uploadedBytes = 0
    let lastUpdate = Date.now()
    let speed = 0

    logger.info('UPLOAD', `开始上传: ${localZipPath} -> ${remoteZipPath} (${(totalSize / 1024 / 1024).toFixed(2)}MB)`)

    await new Promise((resolve, reject) => {
      // 确保远程目录存在
      conn.exec(`mkdir -p ${path.dirname(remoteZipPath)}`, (mkdirErr) => {
        if (mkdirErr) {
          logger.error('REMOTE', `创建远程目录失败: ${path.dirname(remoteZipPath)}`, mkdirErr)
          return reject(mkdirErr)
        }

        conn.sftp((sftpErr, sftp) => {
          if (sftpErr) {
            logger.error('SFTP', 'SFTP初始化失败', sftpErr)
            return reject(sftpErr)
          }

          const writeStream = sftp.createWriteStream(remoteZipPath)
          const fileStream = fs.createReadStream(localZipPath)
          
          writeStream.on('close', () => {
            const uploadTime = (Date.now() - lastUpdate) / 1000
            logger.info('UPLOAD', `上传完成 (${(totalSize / 1024 / 1024).toFixed(2)}MB, ${speed.toFixed(2)}KB/s)`)
            resolve()
          })
          
          writeStream.on('error', (writeErr) => {
            logger.error('UPLOAD', `上传失败: ${remoteZipPath}`, writeErr)
            reject(writeErr)
          })
          
          fileStream.on('data', (chunk) => {
            uploadedBytes += chunk.length
            const now = Date.now()
            const elapsed = (now - lastUpdate) / 1000 // 秒
            
            if (elapsed > 0.5) {
              speed = (chunk.length / 1024) / elapsed
              const percent = ((uploadedBytes / totalSize) * 100).toFixed(1)
              process.stdout.write(`\r上传进度: ${percent}% | ${(uploadedBytes/1024/1024).toFixed(2)}MB/${(totalSize/1024/1024).toFixed(2)}MB | ${speed.toFixed(2)}KB/s`)
              lastUpdate = now
            }
          })

          fileStream.pipe(writeStream)
        })
      })
    })

    // 解压阶段
    logger.info('UNZIP', '开始在服务器解压文件...')
    
    await new Promise((resolve, reject) => {
      conn.exec(
        `mkdir -p ${remotePath} && unzip -o \"${remoteZipPath}\" -d \"${remotePath}\" && rm \"${remoteZipPath}\"`,
        (err, stream) => {
          if (err) {
            logger.error('UNZIP', '解压命令执行失败', err)
            return reject(err)
          }

          let stdout = ''
          let stderr = ''

          stream
            .on('data', (data) => {
              stdout += data
            })
            .stderr.on('data', (data) => {
              stderr += data
              logger.error('UNZIP', `解压错误: ${data}`)
            })

          stream.on('close', (code) => {
            if (code !== 0) {
              logger.error('UNZIP', `解压失败 (code: ${code})`, new Error(stderr))
              return reject(new Error(stderr))
            }
            
            logger.info('UNZIP', '解压完成')
            resolve()
          })
        }
      )
    })

    rl.close()
    logger.info('MAIN', '发布流程全部完成!')
    conn.end()
  } catch (error) {
    logger.error('MAIN', '发布流程失败', error)
    rl.close()
    process.exit(1)
  }
}

try {
  main()
} catch (error) {
  logger.error('MAIN', '未捕获的全局错误', error)
}
