#!/usr/bin/env node
const readline = require('readline')
const fs = require('fs')
const path = require('path')
const PublishTool = require('./PublishTool')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// 日志系统
class Logger {
  constructor() {
    this.logStream = fs.createWriteStream('deploy.log', { flags: 'a' })
    this.logLevels = {
      INFO: 1,
      WARN: 2,
      ERROR: 3
    }
    this.currentLevel = this.logLevels.INFO
  }

  log(level, module, message) {
    const timestamp = new Date().toISOString()
    const levelStr = Object.keys(this.logLevels).find(key => this.logLevels[key] === level)
    const logLine = `[${timestamp}] [${levelStr}] [${module}] ${message}`

    // 输出到控制台
    if (level >= this.currentLevel) {
      if (level === this.logLevels.ERROR) {
        console.error(logLine)
      } else {
        console.log(logLine)
      }
    }

    // 写入日志文件
    this.logStream.write(logLine + '\n')
  }

  info(module, message) {
    this.log(this.logLevels.INFO, module, message)
  }

  warn(module, message) {
    this.log(this.logLevels.WARN, module, message)
  }

  error(module, message, error = null) {
    let fullMessage = message
    if (error) {
      fullMessage += `\n${error.stack || error.message}`
    }
    this.log(this.logLevels.ERROR, module, fullMessage)
  }
}

const logger = new Logger()

// 获取用户输入
function ask(question, hidden = false) {
  return new Promise((resolve) => {
    if (hidden) {
      const stdin = process.openStdin()
      process.stdin.on('data', (char) => {
        char = char + ''
        switch (char) {
          case '\n':
          case '\r':
          case '\u0004':
            stdin.pause()
            break
          default:
            process.stdout.write(
              '\x1B[2K\x1B[200D' + question + Array(rl.line.length + 1).join('*')
            )
            break
        }
      })
    }

    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

async function main() {
  try {
    logger.info('MAIN', '=== 文件发布脚本启动 ===')

    // 获取部署参数
    const serverIp = await ask('请输入目标服务器IP地址: ')
    const password = await ask('请输入root密码: ', true)
    const username = process.env.DEPLOY_USER || 'root'
    logger.info('AUTH', `使用账号: ${username}@${serverIp}`)

    // 创建发布工具实例
    const publishTool = new PublishTool({
      sourcePath: '../dist-prod',
      remotePath: '/var/www/html',
      zipName: 'release.zip',
      server: {
        host: serverIp,
        username: username,
        password: password,
        privateKeyPath: process.env.DEPLOY_KEY
      },
      logger: logger
    })

    // 执行发布
    await publishTool.deploy()

    rl.close()
    logger.info('MAIN', '发布流程全部完成!')
  } catch (error) {
    logger.error('MAIN', '发布流程失败', error)
    rl.close()
    process.exit(1)
  }
}

try {
  main()
} catch (error) {
  logger.error('MAIN', '未捕获的全局错误', error)
}
