const JSZip = require('jszip');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

/**
 * 将文件夹打包为zip文件（改进版）
 * @param {string} sourceDir 要打包的源文件夹路径
 * @param {string} outPath 输出的zip文件路径
 * @param {object} [options] 压缩选项
 * @param {number} [options.compressionLevel=9] 压缩级别 (0-9)
 * @returns {Promise<void>}
 */
async function zipFolder(sourceDir, outPath, options = {}) {
  const { compressionLevel = 9 } = options;
  const zip = new JSZip();
  
  // 递归添加文件夹内容到zip
  async function addFolderToZip(folderPath, zipPath) {
    try {
      const files = await readdir(folderPath);
      
      for (const file of files) {
        const fullPath = path.join(folderPath, file);
        const relativePath = path.join(zipPath, file);
        const stats = await stat(fullPath);
        
        // 跳过符号链接
        if (stats.isSymbolicLink()) {
          continue;
        }
        
        if (stats.isDirectory()) {
          await addFolderToZip(fullPath, relativePath);
        } else {
          // 使用流式读取处理大文件
          const readStream = fs.createReadStream(fullPath);
          zip.file(relativePath, readStream, { stream: true });
        }
      }
    } catch (error) {
      console.error(`处理文件夹时出错: ${folderPath}`, error);
      throw error;
    }
  }
  
  try {
    // 检查并删除已存在的输出文件
    try {
      await promisify(fs.access)(outPath, fs.constants.F_OK);
      await promisify(fs.unlink)(outPath);
      console.log(`已删除已存在的文件: ${outPath}`);
    } catch (accessError) {
      if (accessError.code !== 'ENOENT') {
        throw accessError;
      }
    }

    await addFolderToZip(sourceDir, '');
    
    // 生成zip文件
    const content = await zip.generateAsync({
      type: 'nodebuffer',
      compression: 'DEFLATE',
      compressionOptions: { level: compressionLevel }
    });
    
    await promisify(fs.writeFile)(outPath, content);
    console.log(`成功创建zip文件: ${outPath}`);
  } catch (error) {
    console.error('压缩过程中出错:', error);
    throw error;
  }
}

// 使用示例
// zipFolder('./要打包的文件夹', './output.zip', { compressionLevel: 6 }).catch(console.error);

module.exports = zipFolder;
