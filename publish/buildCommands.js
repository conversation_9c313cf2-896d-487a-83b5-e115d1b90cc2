const fs = require('fs')
const path = require('path')
const util = require('util')
const { exec } = require('child_process')

async function cleanAndBuild(sourcePath, logger) {
  try {
    logger.info('BUILD', '构建开始')
    // 执行全新构建
    const execAsync = util.promisify(exec)
    
    // 执行生产环境构建命令
    const { stdout, stderr } = await execAsync('npm run build:prod')
    
    // 记录构建输出
    if (stdout) logger.info('BUILD', `构建输出: ${stdout.trim()}`)
    if (stderr) logger.warn('BUILD', `构建警告: ${stderr.trim()}`)
    
    logger.info('BUILD', '全新构建完成')
    return true
  } catch (err) {
    // 详细记录构建错误
    logger.error('BUILD', '构建刷新失败', err)
    if (err.stderr) logger.error('BUILD', `构建错误输出: ${err.stderr}`)
    if (err.stdout) logger.error('BUILD', `构建标准输出: ${err.stdout}`)
    throw err
  }
}

module.exports = {
  cleanAndBuild
}
